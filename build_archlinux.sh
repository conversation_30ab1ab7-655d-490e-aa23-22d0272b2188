#!/bin/bash

# PixivTagDownloader ArchLinux 构建脚本
# 此脚本用于在ArchLinux系统上自动安装依赖并编译项目

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为ArchLinux系统
check_archlinux() {
    if [ ! -f /etc/arch-release ]; then
        print_error "此脚本仅适用于ArchLinux系统"
        exit 1
    fi
    print_info "检测到ArchLinux系统"
}

# 检查是否有sudo权限
check_sudo() {
    if ! sudo -n true 2>/dev/null; then
        print_info "需要sudo权限来安装依赖包"
        sudo -v
    fi
}

# 安装依赖包
install_dependencies() {
    print_info "正在安装依赖包..."
    
    # 基础构建工具
    local base_packages=(
        "base-devel"
        "cmake"
        "gcc"
        "git"
    )
    
    # 项目依赖
    local project_packages=(
        "curl"
        "nlohmann-json"
        "yaml-cpp"
        "spdlog"
        "cli11"
    )
    
    # 可选依赖
    local optional_packages=(
        "aria2"  # 用于aria2c下载方式
    )
    
    print_info "安装基础构建工具..."
    sudo pacman -S --needed --noconfirm "${base_packages[@]}"
    
    print_info "安装项目依赖..."
    sudo pacman -S --needed --noconfirm "${project_packages[@]}"
    
    print_info "安装可选依赖..."
    sudo pacman -S --needed --noconfirm "${optional_packages[@]}" || {
        print_warning "部分可选依赖安装失败，但不影响编译"
    }
    
    print_success "依赖包安装完成"
}

# 检查依赖是否已安装
check_dependencies() {
    print_info "检查依赖包..."
    
    local missing_packages=()
    local required_packages=(
        "cmake"
        "gcc"
        "curl"
        "nlohmann-json"
        "yaml-cpp"
        "spdlog"
        "cli11"
    )
    
    for package in "${required_packages[@]}"; do
        if ! pacman -Qi "$package" &>/dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        print_warning "缺少以下依赖包: ${missing_packages[*]}"
        return 1
    fi
    
    print_success "所有依赖包已安装"
    return 0
}

# 创建构建目录
create_build_dir() {
    print_info "创建构建目录..."
    
    if [ -d "build" ]; then
        print_warning "构建目录已存在，将清理并重新创建"
        rm -rf build
    fi
    
    mkdir -p build
    print_success "构建目录创建完成"
}

# 配置CMake
configure_cmake() {
    print_info "配置CMake..."
    
    cd build
    
    # CMake配置选项
    local cmake_options=(
        "-DCMAKE_BUILD_TYPE=Release"
        "-DCMAKE_CXX_STANDARD=20"
        "-DCMAKE_CXX_STANDARD_REQUIRED=ON"
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
    )
    
    cmake "${cmake_options[@]}" .. || {
        print_error "CMake配置失败"
        exit 1
    }
    
    cd ..
    print_success "CMake配置完成"
}

# 编译项目
build_project() {
    print_info "开始编译项目..."
    
    cd build
    
    # 获取CPU核心数用于并行编译
    local cpu_cores=$(nproc)
    print_info "使用 $cpu_cores 个CPU核心进行并行编译"
    
    make -j"$cpu_cores" || {
        print_error "编译失败"
        exit 1
    }
    
    cd ..
    print_success "项目编译完成"
}

# 运行测试（如果有）
run_tests() {
    print_info "检查是否有测试..."
    
    if [ -f "build/tests" ]; then
        print_info "运行测试..."
        cd build
        ctest --output-on-failure || {
            print_warning "部分测试失败"
        }
        cd ..
    else
        print_info "未找到测试程序"
    fi
}

# 安装程序
install_program() {
    print_info "是否要安装程序到系统？(y/N)"
    read -r response
    
    if [[ "$response" =~ ^[Yy]$ ]]; then
        print_info "安装程序..."
        cd build
        sudo make install || {
            print_error "安装失败"
            exit 1
        }
        cd ..
        print_success "程序安装完成"
    else
        print_info "跳过安装，可执行文件位于: build/PixivTagDownloader"
    fi
}

# 创建示例配置文件
create_config() {
    if [ ! -f "config.yaml" ] && [ -f "config.yaml.example" ]; then
        print_info "创建配置文件..."
        cp config.yaml.example config.yaml
        print_success "配置文件已创建: config.yaml"
        print_warning "请编辑 config.yaml 文件，特别是设置 pixiv_cookie"
    fi
}

# 显示使用说明
show_usage() {
    print_success "构建完成！"
    echo
    print_info "使用方法："
    echo "  1. 编辑配置文件: nano config.yaml"
    echo "  2. 设置Pixiv Cookie（必需）"
    echo "  3. 运行程序:"
    echo "     - 交互模式: ./build/PixivTagDownloader"
    echo "     - 命令行模式: ./build/PixivTagDownloader -u USER_ID --all-works"
    echo
    print_info "更多信息请查看 README.md"
}

# 主函数
main() {
    print_info "PixivTagDownloader ArchLinux 构建脚本"
    echo
    
    # 检查系统
    check_archlinux
    check_sudo
    
    # 检查并安装依赖
    if ! check_dependencies; then
        print_info "正在安装缺失的依赖..."
        install_dependencies
    fi
    
    # 构建项目
    create_build_dir
    configure_cmake
    build_project
    
    # 可选步骤
    run_tests
    create_config
    
    # 询问是否安装
    install_program
    
    # 显示使用说明
    show_usage
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
