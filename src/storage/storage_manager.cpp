#include "storage/storage_manager.h"
#include "utilities/logger.h"
#include "utilities/string_utils.h"
#include "utilities/file_utils.h"
#include <regex>
#include <chrono>
#include <iomanip>
#include <sstream>

namespace pixiv_downloader {
namespace storage {

// 静态常量定义
const size_t StorageManager::MAX_FILENAME_LENGTH = 200;
const size_t StorageManager::MAX_PATH_LENGTH = 260;
const std::string StorageManager::ILLEGAL_FILENAME_CHARS = "<>:\"/\\|?*";
const std::string StorageManager::REPLACEMENT_CHAR = "_";

StorageManager::StorageManager(const config::Config& config) : config_(config) {
}

bool StorageManager::Initialize() {
    // 验证输出根目录
    if (!ValidateOutputDirectory()) {
        LOG_ERROR("输出目录验证失败");
        return false;
    }
    
    LOG_INFO("存储管理器初始化成功");
    return true;
}

PathGenerationResult StorageManager::GenerateSingleImagePath(const api::ArtworkInfo& artwork_info,
                                                            const api::UserInfo& user_info,
                                                            const std::string& file_extension) {
    auto variables = GenerateTemplateVariables(artwork_info, user_info, 0, file_extension);
    
    // 处理作品路径模板
    std::string artwork_path = ProcessTemplate(config_.paths.artwork_path_template, variables);
    LOG_DEBUG("单图路径生成 - 模板: '{}', 生成路径: '{}'", config_.paths.artwork_path_template, artwork_path);

    // 处理单图命名模板
    std::string filename = ProcessTemplate(config_.paths.single_image_naming_template, variables);
    
    // 组合完整路径
    std::string full_path = utilities::FileUtils::JoinPath(artwork_path, filename);
    
    // 安全化路径
    full_path = SanitizePath(full_path);
    
    // 检查路径长度
    return TruncatePathIfNeeded(full_path);
}

PathGenerationResult StorageManager::GenerateMultiImagePath(const api::ArtworkInfo& artwork_info,
                                                           const api::UserInfo& user_info,
                                                           int page_index,
                                                           const std::string& file_extension) {
    auto variables = GenerateTemplateVariables(artwork_info, user_info, page_index, file_extension);
    
    // 处理作品路径模板
    std::string artwork_path = ProcessTemplate(config_.paths.artwork_path_template, variables);
    
    // 处理多图子文件夹模板
    std::string subfolder = ProcessTemplate(config_.paths.multi_image_subfolder_naming_template, variables);
    
    // 处理多图文件命名模板
    std::string filename = ProcessTemplate(config_.paths.multi_image_file_naming_template, variables);
    
    // 组合完整路径
    std::string full_path = utilities::FileUtils::JoinPath({artwork_path, subfolder, filename});
    
    // 安全化路径
    full_path = SanitizePath(full_path);
    
    // 检查路径长度
    return TruncatePathIfNeeded(full_path);
}

PathGenerationResult StorageManager::GenerateNovelPath(const api::ArtworkInfo& artwork_info,
                                                      const api::UserInfo& user_info) {
    auto variables = GenerateTemplateVariables(artwork_info, user_info, -1, ".txt");
    
    // 处理小说路径模板
    std::string novel_path = ProcessTemplate(config_.paths.novel_path_template, variables);
    
    // 处理小说命名模板
    std::string filename = ProcessTemplate(config_.paths.novel_naming_template, variables);
    
    // 组合完整路径
    std::string full_path = utilities::FileUtils::JoinPath(novel_path, filename);
    
    // 安全化路径
    full_path = SanitizePath(full_path);
    
    // 检查路径长度
    return TruncatePathIfNeeded(full_path);
}

PathGenerationResult StorageManager::GenerateMetadataPath(const api::ArtworkInfo& artwork_info,
                                                         const api::UserInfo& user_info,
                                                         const std::string& base_path) {
    auto variables = GenerateTemplateVariables(artwork_info, user_info);

    std::string metadata_filename;

    // 根据作品类型和页数决定metadata文件名
    if (artwork_info.type == config::ArtworkType::NOVEL) {
        // 小说：使用小说文件名模板，但扩展名改为.txt
        std::string novel_filename = ProcessTemplate(config_.paths.novel_naming_template, variables);
        metadata_filename = utilities::FileUtils::GetFileNameWithoutExtension(novel_filename) + "_info.txt";
    } else if (artwork_info.page_count > 1) {
        // 多图：使用目录名作为metadata文件名
        std::string subfolder_name = ProcessTemplate(config_.paths.multi_image_subfolder_naming_template, variables);
        // 移除末尾的斜杠
        if (!subfolder_name.empty() && subfolder_name.back() == '/') {
            subfolder_name.pop_back();
        }
        metadata_filename = subfolder_name + "_info.txt";
    } else {
        // 单图：使用图片文件名，但扩展名改为.txt
        std::string image_filename = ProcessTemplate(config_.paths.single_image_naming_template, variables);
        metadata_filename = utilities::FileUtils::GetFileNameWithoutExtension(image_filename) + "_info.txt";
    }

    std::string full_path;
    if (base_path.empty()) {
        // 使用默认路径
        if (artwork_info.type == config::ArtworkType::NOVEL) {
            std::string novel_path = ProcessTemplate(config_.paths.novel_path_template, variables);
            full_path = utilities::FileUtils::JoinPath(novel_path, metadata_filename);
        } else {
            std::string artwork_path = ProcessTemplate(config_.paths.artwork_path_template, variables);
            if (artwork_info.page_count > 1) {
                // 多图：metadata文件放在子文件夹内
                std::string subfolder = ProcessTemplate(config_.paths.multi_image_subfolder_naming_template, variables);
                full_path = utilities::FileUtils::JoinPath({artwork_path, subfolder, metadata_filename});
            } else {
                // 单图：metadata文件与图片文件在同一目录
                full_path = utilities::FileUtils::JoinPath(artwork_path, metadata_filename);
            }
        }
    } else {
        // 使用指定的基础路径
        std::string dir = utilities::FileUtils::GetDirectoryPath(base_path);
        full_path = utilities::FileUtils::JoinPath(dir, metadata_filename);
    }

    // 安全化路径
    full_path = SanitizePath(full_path);

    // 检查路径长度
    return TruncatePathIfNeeded(full_path);
}

bool StorageManager::CreateDirectory(const std::string& directory_path) {
    return utilities::FileUtils::CreateDirectory(directory_path);
}

std::string StorageManager::HandleFileConflict(const std::string& file_path, 
                                              config::FileConflictStrategy strategy) {
    if (!FileExists(file_path)) {
        return file_path; // 没有冲突
    }
    
    switch (strategy) {
        case config::FileConflictStrategy::SKIP:
            LOG_INFO("文件已存在，跳过: {}", file_path);
            return ""; // 返回空字符串表示跳过
            
        case config::FileConflictStrategy::OVERWRITE:
            LOG_INFO("文件已存在，覆盖: {}", file_path);
            return file_path;
            
        case config::FileConflictStrategy::RENAME:
            return GenerateUniqueFileName(file_path);
            
        default:
            return file_path;
    }
}

bool StorageManager::CreateArtworkMetadata(const api::ArtworkInfo& artwork_info,
                                          const api::UserInfo& user_info,
                                          const std::string& metadata_path) {
    try {
        std::string content = CreateMetadataContent(artwork_info, user_info, false);
        
        // 创建目录
        std::string dir = utilities::FileUtils::GetDirectoryPath(metadata_path);
        if (!CreateDirectory(dir)) {
            LOG_ERROR("创建元数据目录失败: {}", dir);
            return false;
        }
        
        // 写入文件
        if (!utilities::FileUtils::WriteStringToFile(metadata_path, content)) {
            LOG_ERROR("写入元数据文件失败: {}", metadata_path);
            return false;
        }
        
        LOG_DEBUG("元数据文件创建成功: {}", metadata_path);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("创建元数据文件时发生异常: {}", e.what());
        return false;
    }
}

bool StorageManager::CreateNovelMetadata(const api::ArtworkInfo& artwork_info,
                                        const api::UserInfo& user_info,
                                        const std::string& metadata_path) {
    try {
        std::string content = CreateMetadataContent(artwork_info, user_info, true);
        
        // 创建目录
        std::string dir = utilities::FileUtils::GetDirectoryPath(metadata_path);
        if (!CreateDirectory(dir)) {
            LOG_ERROR("创建小说目录失败: {}", dir);
            return false;
        }
        
        // 写入文件
        if (!utilities::FileUtils::WriteStringToFile(metadata_path, content)) {
            LOG_ERROR("写入小说文件失败: {}", metadata_path);
            return false;
        }
        
        LOG_DEBUG("小说文件创建成功: {}", metadata_path);
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("创建小说文件时发生异常: {}", e.what());
        return false;
    }
}

bool StorageManager::ValidateOutputDirectory() {
    const std::string& output_dir = config_.paths.output_root_dir;
    
    if (output_dir.empty()) {
        LOG_ERROR("输出根目录未配置");
        return false;
    }
    
    // 尝试创建目录
    if (!CreateDirectory(output_dir)) {
        LOG_ERROR("无法创建或访问输出目录: {}", output_dir);
        return false;
    }
    
    // 检查写入权限
    std::string test_file = utilities::FileUtils::JoinPath(output_dir, ".test_write");
    if (!utilities::FileUtils::WriteStringToFile(test_file, "test")) {
        LOG_ERROR("输出目录没有写入权限: {}", output_dir);
        return false;
    }
    utilities::FileUtils::DeleteFile(test_file);
    
    LOG_INFO("输出目录验证成功: {}", output_dir);
    return true;
}

size_t StorageManager::GetFileSize(const std::string& file_path) {
    return utilities::FileUtils::GetFileSize(file_path);
}

bool StorageManager::FileExists(const std::string& file_path) {
    return utilities::FileUtils::FileExists(file_path);
}

bool StorageManager::DirectoryExists(const std::string& directory_path) {
    return utilities::FileUtils::DirectoryExists(directory_path);
}

// 私有方法实现
std::string StorageManager::ProcessTemplate(const std::string& template_str, 
                                           const TemplateVariables& variables) {
    std::string result = template_str;
    
    // 替换模板变量
    for (const auto& [key, value] : variables) {
        std::string placeholder = "{" + key + "}";
        result = utilities::StringUtils::ReplaceAll(result, placeholder, value);
    }
    
    // 处理条件模板（如 {series_title|No_Series}）
    std::regex conditional_pattern(R"(\{([^|}]+)\|([^}]+)\})");
    std::smatch match;
    
    while (std::regex_search(result, match, conditional_pattern)) {
        std::string var_name = match[1].str();
        std::string default_value = match[2].str();
        
        auto it = variables.find(var_name);
        std::string replacement = (it != variables.end() && !it->second.empty()) 
                                 ? it->second : default_value;
        
        result = utilities::StringUtils::ReplaceAll(result, match[0].str(), replacement);
    }
    
    return result;
}

TemplateVariables StorageManager::GenerateTemplateVariables(const api::ArtworkInfo& artwork_info,
                                                           const api::UserInfo& user_info,
                                                           int page_index,
                                                           const std::string& file_extension) {
    TemplateVariables variables;
    
    // 基本信息
    variables["uid"] = user_info.uid;
    variables["username"] = SanitizeFileName(user_info.username);
    variables["pid"] = artwork_info.pid;
    variables["title"] = SanitizeFileName(artwork_info.title);
    variables["type"] = config::ConfigManager::ArtworkTypeToString(artwork_info.type);
    variables["ext"] = file_extension;
    
    // 页面信息
    if (page_index >= 0) {
        variables["page_index"] = FormatPageIndex(page_index, config_.paths.page_index_format);
    }
    variables["page_count"] = std::to_string(artwork_info.page_count);
    
    // 日期信息
    variables["upload_date"] = FormatDate(artwork_info.upload_date, config_.paths.date_format);
    
    // 标签信息
    variables["tags"] = JoinTags(artwork_info.tags, config_.paths.tag_separator);
    
    // 其他信息
    variables["r18"] = artwork_info.is_r18 ? "R18" : "";
    variables["like_count"] = std::to_string(artwork_info.like_count);
    variables["bookmark_count"] = std::to_string(artwork_info.bookmark_count);
    variables["series_title"] = SanitizeFileName(artwork_info.series_title);
    variables["series_id"] = artwork_info.series_id;
    
    // 输出根目录
    variables["output_root_dir"] = config_.paths.output_root_dir;

    // 调试信息：记录关键变量
    LOG_DEBUG("路径生成变量 - output_root_dir: '{}', uid: '{}', username: '{}', type: '{}'",
              variables["output_root_dir"], variables["uid"], variables["username"], variables["type"]);

    // 安全检查：确保关键变量不为空或异常值
    if (variables["output_root_dir"].empty() || variables["output_root_dir"] == "unnamed") {
        LOG_ERROR("路径生成错误：output_root_dir为空或异常值 '{}'", variables["output_root_dir"]);
        LOG_ERROR("配置中的output_root_dir: '{}'", config_.paths.output_root_dir);
    }

    if (variables["uid"].empty()) {
        LOG_WARN("路径生成警告：用户ID为空");
    }

    if (variables["username"] == "unknown_user") {
        LOG_WARN("路径生成警告：用户名为unknown_user，可能是用户信息获取失败");
    }

    return variables;
}

std::string StorageManager::SanitizeFileName(const std::string& filename) {
    return utilities::StringUtils::SanitizeFileName(filename, REPLACEMENT_CHAR);
}

std::string StorageManager::SanitizePath(const std::string& path) {
    // 分割路径组件并分别安全化
    std::vector<std::string> components;
    std::string current_path = path;
    
    while (!current_path.empty()) {
        std::string component = utilities::FileUtils::GetFileName(current_path);
        if (!component.empty()) {
            components.insert(components.begin(), SanitizeFileName(component));
        }
        
        std::string parent = utilities::FileUtils::GetDirectoryPath(current_path);
        if (parent == current_path) break;
        current_path = parent;
    }
    
    return utilities::FileUtils::JoinPath(components);
}

PathGenerationResult StorageManager::TruncatePathIfNeeded(const std::string& path) {
    PathGenerationResult result;
    result.original_path = path;
    result.full_path = path;
    result.directory = utilities::FileUtils::GetDirectoryPath(path);
    result.filename = utilities::FileUtils::GetFileName(path);
    
    // 检查路径长度
    if (path.length() > MAX_PATH_LENGTH) {
        result.path_truncated = true;
        
        // 截断文件名部分
        std::string dir = result.directory;
        std::string name = utilities::FileUtils::GetFileNameWithoutExtension(result.filename);
        std::string ext = utilities::FileUtils::GetFileExtension(result.filename);
        
        size_t available_length = MAX_PATH_LENGTH - dir.length() - ext.length() - 10; // 留一些余量
        if (name.length() > available_length) {
            name = utilities::StringUtils::Truncate(name, available_length, "");
            result.filename_truncated = true;
        }
        
        result.filename = name + ext;
        result.full_path = utilities::FileUtils::JoinPath(dir, result.filename);
    }
    
    return result;
}

std::string StorageManager::FormatDate(const std::string& date_str, const std::string& /*format*/) {
    // 简化实现：假设输入是ISO格式，输出为YYYYMMDD
    if (date_str.length() >= 10) {
        std::string year = date_str.substr(0, 4);
        std::string month = date_str.substr(5, 2);
        std::string day = date_str.substr(8, 2);
        return year + month + day;
    }
    return date_str;
}

std::string StorageManager::FormatPageIndex(int page_index, const std::string& format) {
    char buffer[32];
    snprintf(buffer, sizeof(buffer), format.c_str(), page_index);
    return std::string(buffer);
}

std::string StorageManager::JoinTags(const std::vector<std::string>& tags, const std::string& separator) {
    std::vector<std::string> safe_tags;
    for (const auto& tag : tags) {
        safe_tags.push_back(SanitizeFileName(tag));
    }
    return utilities::StringUtils::Join(safe_tags, separator);
}

std::string StorageManager::GenerateUniqueFileName(const std::string& base_path) {
    return utilities::FileUtils::GenerateUniqueFileName(base_path);
}

std::string StorageManager::CreateMetadataContent(const api::ArtworkInfo& artwork_info,
                                                 const api::UserInfo& /*user_info*/,
                                                 bool is_novel) {
    std::ostringstream oss;
    
    // 基本信息
    oss << "Title: " << artwork_info.title << "\n";
    oss << "Author_UID: " << artwork_info.author_uid << "\n";
    oss << "Author_Username: " << artwork_info.author_username << "\n";
    
    if (is_novel) {
        oss << "Novel_PID: " << artwork_info.pid << "\n";
        oss << "Novel_Type: Novel\n";
        oss << "Word_Count: " << artwork_info.word_count << "\n";
    } else {
        oss << "Artwork_PID: " << artwork_info.pid << "\n";
        oss << "Artwork_Type: " << config::ConfigManager::ArtworkTypeToString(artwork_info.type) << "\n";
        oss << "Page_Count: " << artwork_info.page_count << "\n";
    }
    
    oss << "Upload_Date: " << artwork_info.upload_date << "\n";
    oss << "Tags: " << utilities::StringUtils::Join(artwork_info.tags, ", ") << "\n";
    oss << "R18: " << (artwork_info.is_r18 ? "True" : "False") << "\n";
    oss << "Like_Count: " << artwork_info.like_count << "\n";
    oss << "Bookmark_Count: " << artwork_info.bookmark_count << "\n";
    
    if (!artwork_info.series_title.empty()) {
        oss << "Series_Title: " << artwork_info.series_title << "\n";
        oss << "Series_ID: " << artwork_info.series_id << "\n";
    }
    
    oss << "Description:\n" << artwork_info.description << "\n";
    
    if (!is_novel && !artwork_info.image_urls.empty()) {
        oss << "Original_URLs:\n";
        for (const auto& url : artwork_info.image_urls) {
            oss << url << "\n";
        }
    }
    
    oss << "Download_Time: " << GetCurrentTimestamp() << "\n";
    
    if (is_novel) {
        if (!artwork_info.content.empty()) {
            oss << "*** Content ***\n";
            oss << artwork_info.content << "\n";
            LOG_DEBUG("小说内容已添加到文件，长度: {} 字符", artwork_info.content.length());
        } else {
            oss << "*** Content ***\n";
            oss << "[小说内容为空或获取失败]\n";
            LOG_WARN("小说内容为空，PID: {}", artwork_info.pid);
        }
    }
    
    return oss.str();
}

std::string StorageManager::GetCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::gmtime(&time_t);
    
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%dT%H:%M:%SZ");
    return oss.str();
}

} // namespace storage
} // namespace pixiv_downloader
