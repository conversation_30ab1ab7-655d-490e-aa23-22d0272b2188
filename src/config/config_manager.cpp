#include "config/config_manager.h"
#include "utilities/logger.h"
#include "utilities/string_utils.h"
#include "utilities/file_utils.h"
#include <fstream>
#include <iostream>

namespace pixiv_downloader {
namespace config {

ConfigManager::ConfigManager() : config_file_path_("config.yaml") {
}

bool ConfigManager::LoadConfig(const std::string& config_path) {
    config_file_path_ = config_path;
    
    if (!utilities::FileUtils::FileExists(config_path)) {
        LOG_WARN("配置文件 {} 未找到，将创建默认配置文件", config_path);
        if (!CreateDefaultConfig(config_path)) {
            LOG_ERROR("创建默认配置文件失败");
            return false;
        }
        LOG_INFO("已创建默认配置文件。请编辑该文件（尤其是 Cookie）并重新运行应用程序。");
        return false; // 需要用户编辑配置文件
    }

    try {
        YAML::Node yaml_config = YAML::LoadFile(config_path);
        ParseYamlNode(yaml_config);
        
        if (!ValidateConfig()) {
            LOG_ERROR("配置验证失败");
            return false;
        }
        
        LOG_INFO("配置文件加载成功: {}", config_path);
        LOG_DEBUG("配置加载完成 - output_root_dir: '{}'", config_.paths.output_root_dir);
        return true;
        
    } catch (const YAML::Exception& e) {
        LOG_ERROR("解析配置文件出错: {}。请检查文件语法。", e.what());
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR("加载配置文件时发生错误: {}", e.what());
        return false;
    }
}

bool ConfigManager::CreateDefaultConfig(const std::string& config_path) {
    try {
        std::string default_yaml = GenerateDefaultConfigYaml();
        return utilities::FileUtils::WriteStringToFile(config_path, default_yaml);
    } catch (const std::exception& e) {
        LOG_ERROR("创建默认配置文件失败: {}", e.what());
        return false;
    }
}

bool ConfigManager::ValidateConfig() {
    // 验证Cookie
    if (config_.pixiv_cookie.empty()) {
        LOG_ERROR("Pixiv Cookie 未配置");
        return false;
    }

    // 验证输出目录
    if (config_.paths.output_root_dir.empty()) {
        LOG_ERROR("输出根目录未配置");
        return false;
    }

    // 检查输出目录是否包含可疑值
    if (config_.paths.output_root_dir == "unnamed" || config_.paths.output_root_dir == "unknown_user") {
        LOG_ERROR("输出根目录配置异常: '{}' - 这可能是配置加载错误导致的", config_.paths.output_root_dir);
        return false;
    }

    // 验证并发数
    if (config_.concurrency <= 0) {
        LOG_WARN("并发数无效，使用默认值 4");
        config_.concurrency = 4;
    }

    // 验证延迟配置
    if (config_.delay.min_delay < 0 || config_.delay.max_delay < config_.delay.min_delay) {
        LOG_WARN("延迟配置无效，使用默认值");
        config_.delay.min_delay = 1;
        config_.delay.max_delay = 3;
    }

    // 验证HTTP超时配置
    if (config_.http.connection_timeout <= 0) {
        config_.http.connection_timeout = 30;
    }
    if (config_.http.read_timeout <= 0) {
        config_.http.read_timeout = 60;
    }

    return true;
}

void ConfigManager::ParseYamlNode(const YAML::Node& node) {
    // 认证配置
    if (node["pixiv_cookie"]) {
        config_.pixiv_cookie = node["pixiv_cookie"].as<std::string>();
    }

    if (node["auth"]) {
        const auto& auth_node = node["auth"];
        if (auth_node["skip_online_validation"]) {
            config_.auth.skip_online_validation = auth_node["skip_online_validation"].as<bool>();
        }
        if (auth_node["allow_offline_mode"]) {
            config_.auth.allow_offline_mode = auth_node["allow_offline_mode"].as<bool>();
        }
        if (auth_node["validation_timeout"]) {
            config_.auth.validation_timeout = auth_node["validation_timeout"].as<int>();
        }
        if (auth_node["require_r18_permission"]) {
            config_.auth.require_r18_permission = auth_node["require_r18_permission"].as<bool>();
        }
        if (auth_node["strict_cookie_validation"]) {
            config_.auth.strict_cookie_validation = auth_node["strict_cookie_validation"].as<bool>();
        }
    }

    // 下载配置
    if (node["download_method"]) {
        config_.download_method = StringToDownloadMethod(node["download_method"].as<std::string>());
    }
    if (node["aria2c_path"]) {
        config_.aria2c_path = node["aria2c_path"].as<std::string>();
    }
    if (node["aria2c_options"]) {
        config_.aria2c_options = node["aria2c_options"].as<std::string>();
    }
    if (node["concurrency"]) {
        config_.concurrency = node["concurrency"].as<int>();
    }

    // HTTP配置
    if (node["http"]) {
        const auto& http_node = node["http"];
        if (http_node["user_agent"]) {
            config_.http.user_agent = http_node["user_agent"].as<std::string>();
        }
        if (http_node["referer"]) {
            config_.http.referer = http_node["referer"].as<std::string>();
        }
        if (http_node["connection_timeout"]) {
            config_.http.connection_timeout = http_node["connection_timeout"].as<int>();
        }
        if (http_node["read_timeout"]) {
            config_.http.read_timeout = http_node["read_timeout"].as<int>();
        }
        if (http_node["max_retries"]) {
            config_.http.max_retries = http_node["max_retries"].as<int>();
        }
        if (http_node["retry_delay"]) {
            config_.http.retry_delay = http_node["retry_delay"].as<int>();
        }
        if (http_node["custom_headers"]) {
            for (const auto& header : http_node["custom_headers"]) {
                config_.http.custom_headers[header.first.as<std::string>()] = header.second.as<std::string>();
            }
        }
    }

    // 延迟配置
    if (node["delay"]) {
        const auto& delay_node = node["delay"];
        if (delay_node["min_delay"]) {
            config_.delay.min_delay = delay_node["min_delay"].as<int>();
        }
        if (delay_node["max_delay"]) {
            config_.delay.max_delay = delay_node["max_delay"].as<int>();
        }
    }

    // 文件冲突策略
    if (node["file_conflict"]) {
        config_.file_conflict = StringToFileConflictStrategy(node["file_conflict"].as<std::string>());
    }

    // 路径模板配置
    if (node["paths"]) {
        const auto& paths_node = node["paths"];
        if (paths_node["output_root_dir"]) {
            config_.paths.output_root_dir = paths_node["output_root_dir"].as<std::string>();
        }
        if (paths_node["artwork_path_template"]) {
            config_.paths.artwork_path_template = paths_node["artwork_path_template"].as<std::string>();
        }
        if (paths_node["single_image_naming_template"]) {
            config_.paths.single_image_naming_template = paths_node["single_image_naming_template"].as<std::string>();
        }
        if (paths_node["multi_image_subfolder_naming_template"]) {
            config_.paths.multi_image_subfolder_naming_template = paths_node["multi_image_subfolder_naming_template"].as<std::string>();
        }
        if (paths_node["multi_image_file_naming_template"]) {
            config_.paths.multi_image_file_naming_template = paths_node["multi_image_file_naming_template"].as<std::string>();
        }
        if (paths_node["novel_path_template"]) {
            config_.paths.novel_path_template = paths_node["novel_path_template"].as<std::string>();
        }
        if (paths_node["novel_naming_template"]) {
            config_.paths.novel_naming_template = paths_node["novel_naming_template"].as<std::string>();
        }
        if (paths_node["metadata_filename_template"]) {
            config_.paths.metadata_filename_template = paths_node["metadata_filename_template"].as<std::string>();
        }
        if (paths_node["page_index_format"]) {
            config_.paths.page_index_format = paths_node["page_index_format"].as<std::string>();
        }
        if (paths_node["date_format"]) {
            config_.paths.date_format = paths_node["date_format"].as<std::string>();
        }
        if (paths_node["tag_separator"]) {
            config_.paths.tag_separator = paths_node["tag_separator"].as<std::string>();
        }
    }

    // 日志配置
    if (node["logging"]) {
        const auto& log_node = node["logging"];
        if (log_node["level"]) {
            config_.logging.level = utilities::Logger::StringToLevel(log_node["level"].as<std::string>());
        }
        if (log_node["log_path"]) {
            config_.logging.log_path = log_node["log_path"].as<std::string>();
        }
        if (log_node["max_file_size"]) {
            config_.logging.max_file_size = log_node["max_file_size"].as<size_t>();
        }
        if (log_node["max_files"]) {
            config_.logging.max_files = log_node["max_files"].as<size_t>();
        }
    }

    // 默认命令行参数
    if (node["defaults"]) {
        const auto& defaults_node = node["defaults"];
        if (defaults_node["uid"]) {
            config_.default_uid = defaults_node["uid"].as<std::string>();
        }
        if (defaults_node["tags"]) {
            for (const auto& tag : defaults_node["tags"]) {
                config_.default_tags.push_back(tag.as<std::string>());
            }
        }
        if (defaults_node["tag_logic"]) {
            config_.default_tag_logic = StringToTagFilterLogic(defaults_node["tag_logic"].as<std::string>());
        }
        if (defaults_node["types"]) {
            config_.default_types = StringToArtworkTypes(defaults_node["types"].as<std::string>());
        }
        if (defaults_node["all_works"]) {
            config_.default_all_works = defaults_node["all_works"].as<bool>();
        }
    }
}

std::string ConfigManager::GenerateDefaultConfigYaml() const {
    return R"(# PixivTagDownloader 配置文件
# 请根据需要修改以下配置项

# Pixiv认证Cookie（必需）
# 从浏览器中复制完整的Cookie字符串
pixiv_cookie: ""

# 认证配置
auth:
  skip_online_validation: false    # 跳过在线Cookie验证（用于离线测试）
  allow_offline_mode: true         # 允许离线模式
  validation_timeout: 30           # 验证超时时间（秒）
  require_r18_permission: false    # 是否要求R18权限
  strict_cookie_validation: true   # 严格Cookie验证

# 下载配置
download_method: "direct"  # 下载方式: direct, aria2c
aria2c_path: "aria2c"      # aria2c可执行文件路径
aria2c_options: "--continue=true --max-connection-per-server=4"
concurrency: 4             # 并发下载数

# HTTP配置
http:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
  referer: "https://www.pixiv.net/"
  connection_timeout: 30   # 连接超时（秒）
  read_timeout: 60         # 读取超时（秒）
  max_retries: 3           # 最大重试次数
  retry_delay: 2           # 重试延迟（秒）
  custom_headers: {}       # 自定义HTTP头部

# 请求延迟配置
delay:
  min_delay: 1             # 最小延迟（秒）
  max_delay: 3             # 最大延迟（秒）

# 文件冲突处理策略
file_conflict: "skip"      # skip, overwrite, rename

# 路径和文件命名模板
paths:
  output_root_dir: "./downloads"
  artwork_path_template: "{output_root_dir}/{uid}_{username}/{type}/"
  single_image_naming_template: "{upload_date}_{pid}_p0_{title}{ext}"
  multi_image_subfolder_naming_template: "{pid}_{title}/"
  multi_image_file_naming_template: "{page_index}_{title}{ext}"
  novel_path_template: "{output_root_dir}/{uid}_{username}/Novel/{series_title|No_Series}/"
  novel_naming_template: "{upload_date}_{pid}_{title}.txt"
  metadata_filename_template: "{pid}_info.txt"
  page_index_format: "p%02d"  # 页码格式
  date_format: "%Y%m%d"       # 日期格式
  tag_separator: "_"          # 标签分隔符

# 日志配置
logging:
  level: "info"            # trace, debug, info, warn, error
  log_path: ""             # 日志文件路径（空表示仅控制台输出）
  max_file_size: 10485760  # 最大文件大小（字节）
  max_files: 3             # 最大文件数量

# 默认命令行参数
defaults:
  uid: ""
  tags: []
  tag_logic: "or"          # and, or, not
  types: "all"             # illust, manga, novel, all
  all_works: false
)";
}

// 枚举转换函数实现
DownloadMethod ConfigManager::StringToDownloadMethod(const std::string& str) {
    std::string lower_str = utilities::StringUtils::ToLower(str);
    if (lower_str == "aria2c") return DownloadMethod::ARIA2C;
    return DownloadMethod::DIRECT;
}

std::string ConfigManager::DownloadMethodToString(DownloadMethod method) {
    switch (method) {
        case DownloadMethod::ARIA2C: return "aria2c";
        default: return "direct";
    }
}

FileConflictStrategy ConfigManager::StringToFileConflictStrategy(const std::string& str) {
    std::string lower_str = utilities::StringUtils::ToLower(str);
    if (lower_str == "overwrite") return FileConflictStrategy::OVERWRITE;
    if (lower_str == "rename") return FileConflictStrategy::RENAME;
    return FileConflictStrategy::SKIP;
}

std::string ConfigManager::FileConflictStrategyToString(FileConflictStrategy strategy) {
    switch (strategy) {
        case FileConflictStrategy::OVERWRITE: return "overwrite";
        case FileConflictStrategy::RENAME: return "rename";
        default: return "skip";
    }
}

TagFilterLogic ConfigManager::StringToTagFilterLogic(const std::string& str) {
    std::string lower_str = utilities::StringUtils::ToLower(str);
    if (lower_str == "and") return TagFilterLogic::AND;
    if (lower_str == "not") return TagFilterLogic::NOT;
    return TagFilterLogic::OR;
}

std::string ConfigManager::TagFilterLogicToString(TagFilterLogic logic) {
    switch (logic) {
        case TagFilterLogic::AND: return "and";
        case TagFilterLogic::NOT: return "not";
        default: return "or";
    }
}

ArtworkType ConfigManager::StringToArtworkType(const std::string& str) {
    std::string lower_str = utilities::StringUtils::ToLower(str);
    if (lower_str == "illust") return ArtworkType::ILLUST;
    if (lower_str == "manga") return ArtworkType::MANGA;
    if (lower_str == "novel") return ArtworkType::NOVEL;
    return ArtworkType::ALL;
}

std::string ConfigManager::ArtworkTypeToString(ArtworkType type) {
    switch (type) {
        case ArtworkType::ILLUST: return "illust";
        case ArtworkType::MANGA: return "manga";
        case ArtworkType::NOVEL: return "novel";
        default: return "all";
    }
}

std::vector<ArtworkType> ConfigManager::StringToArtworkTypes(const std::string& str) {
    std::vector<ArtworkType> types;
    auto type_strings = utilities::StringUtils::Split(str, ",");
    
    for (auto& type_str : type_strings) {
        type_str = utilities::StringUtils::Trim(type_str);
        ArtworkType type = StringToArtworkType(type_str);
        if (type == ArtworkType::ALL) {
            return {ArtworkType::ALL};
        }
        types.push_back(type);
    }
    
    if (types.empty()) {
        types.push_back(ArtworkType::ALL);
    }
    
    return types;
}

std::string ConfigManager::ArtworkTypesToString(const std::vector<ArtworkType>& types) {
    std::vector<std::string> type_strings;
    for (const auto& type : types) {
        type_strings.push_back(ArtworkTypeToString(type));
    }
    return utilities::StringUtils::Join(type_strings, ",");
}

} // namespace config
} // namespace pixiv_downloader
