#include "cli/cli_handler.h"
#include "api/pixiv_api_client.h"
#include "utilities/logger.h"
#include "utilities/string_utils.h"
#include <iostream>
#include <iomanip>
#include <csignal>
#include <sstream>
#include <algorithm>

namespace pixiv_downloader {
namespace cli {

// 静态成员初始化
CliHandler* CliHandler::instance_ = nullptr;
download::DownloaderCore* CliHandler::downloader_instance_ = nullptr;

CliHandler::CliHandler(config::ConfigManager& config_manager)
    : config_manager_(config_manager), api_client_(nullptr), signal_received_(false), signal_count_(0) {

    instance_ = this;

    // 创建CLI应用
    app_ = std::make_unique<CLI::App>("PixivTagDownloader - Pixiv作品下载器");

    // 创建进度显示器
    progress_display_ = std::make_unique<ProgressDisplay>();
}

CliHandler::<PERSON><PERSON><PERSON><PERSON><PERSON>(config::ConfigManager& config_manager, api::PixivApiClient* api_client)
    : config_manager_(config_manager), api_client_(api_client), signal_received_(false), signal_count_(0) {

    instance_ = this;

    // 创建CLI应用
    app_ = std::make_unique<CLI::App>("PixivTagDownloader - Pixiv作品下载器");

    // 创建进度显示器
    progress_display_ = std::make_unique<ProgressDisplay>();
}

CliHandler::~CliHandler() {
    instance_ = nullptr;
    downloader_instance_ = nullptr;
}

CommandLineArgs CliHandler::ParseCommandLine(int argc, char* argv[]) {
    CommandLineArgs args;
    
    // 配置命令行选项
    app_->add_option("-u,--uid", args.uid, "目标Pixiv用户ID（必需）");
    app_->add_option("-t,--tags", args.tags, "标签列表（逗号分隔）");
    app_->add_option("-l,--logic", args.tag_logic, "标签过滤逻辑 (and|or|not)")
        ->default_val("or");
    app_->add_flag("--all-works", args.all_works, "下载所有作品");
    app_->add_option("-T,--type", args.artwork_types, "作品类型 (illust|manga|novel|all)")
        ->default_val("all");
    app_->add_option("-c,--config", args.config_path, "配置文件路径")
        ->default_val("config.yaml");
    app_->add_option("--output-dir", args.output_dir, "输出根目录");
    app_->add_option("--download-method", args.download_method, "下载方式 (direct|aria2c)");
    app_->add_option("--threads,--concurrency", args.concurrency, "并发下载数");
    app_->add_option("--delay", args.delay_range, "随机延迟范围 (如: 1-3)");
    app_->add_option("--file-conflict", args.file_conflict, "文件冲突策略 (skip|overwrite|rename)");
    app_->add_option("--log-level", args.log_level, "日志级别 (trace|debug|info|warn|error)");
    app_->add_option("--log-path", args.log_path, "日志文件路径");
    app_->add_flag("-i,--interactive", args.interactive, "强制交互模式");
    app_->add_flag("--version", args.version, "显示版本信息");
    app_->add_flag("--diagnose", args.diagnose, "运行配置诊断");
    
    try {
        app_->parse(argc, argv);
    } catch (const CLI::ParseError& e) {
        std::cerr << "命令行参数解析错误: " << e.what() << std::endl;
        args.help = true;
    }
    
    return args;
}

UserSelection CliHandler::RunInteractiveMode() {
    UserSelection selection;

    try {
        std::cout << "\n=== PixivTagDownloader 交互模式 ===" << std::endl;
        std::cout << "💡 提示：在任何时候按 Ctrl+C 可以退出程序" << std::endl;

        // 1. 选择作品类型
        selection.artwork_types = SelectArtworkTypes();

        // 2. 输入用户ID
        selection.uid = InputUserId();

        // 3. 选择下载方式
        int download_method = SelectDownloadMethod();

        if (download_method == 1) {
            // 下载所有作品
            selection.download_all_works = true;
        } else if (download_method == 2) {
            // 手动输入标签
            selection.tags = InputTagsManually();
            if (!selection.tags.empty()) {
                selection.tag_logic = SelectTagFilterLogic();
            }
        } else if (download_method == 3) {
            // 从用户作品中选择标签
            selection.tags = SelectTagsFromUser(selection.uid);
            if (!selection.tags.empty()) {
                selection.tag_logic = SelectTagFilterLogic();
            }
        }

    } catch (const std::runtime_error& e) {
        std::cout << "\n❌ 交互模式错误: " << e.what() << std::endl;
        std::cout << "程序将退出。" << std::endl;
        throw;
    } catch (const std::exception& e) {
        std::cout << "\n❌ 未知错误: " << e.what() << std::endl;
        std::cout << "程序将退出。" << std::endl;
        throw;
    }

    return selection;
}

UserSelection CliHandler::CreateSelectionFromArgs(const CommandLineArgs& args) {
    UserSelection selection;
    
    selection.uid = args.uid;
    selection.download_all_works = args.all_works;
    
    // 解析标签
    if (!args.tags.empty()) {
        for (const auto& tag_str : args.tags) {
            auto tags = utilities::StringUtils::Split(tag_str, ",");
            for (auto& tag : tags) {
                tag = utilities::StringUtils::Trim(tag);
                if (!tag.empty()) {
                    selection.tags.push_back(tag);
                }
            }
        }
    }
    
    // 解析标签逻辑
    selection.tag_logic = config::ConfigManager::StringToTagFilterLogic(args.tag_logic);
    
    // 解析作品类型
    selection.artwork_types = config::ConfigManager::StringToArtworkTypes(args.artwork_types);
    
    return selection;
}

void CliHandler::ShowHelp() {
    std::cout << app_->help() << std::endl;
    
    std::cout << "\n使用示例:" << std::endl;
    std::cout << "  # 交互模式" << std::endl;
    std::cout << "  PixivTagDownloader" << std::endl;
    std::cout << "  " << std::endl;
    std::cout << "  # 下载指定用户的所有作品" << std::endl;
    std::cout << "  PixivTagDownloader -u 12345678 --all-works" << std::endl;
    std::cout << "  " << std::endl;
    std::cout << "  # 下载指定用户的特定标签作品" << std::endl;
    std::cout << "  PixivTagDownloader -u 12345678 -t \"原创,插画\" -l or" << std::endl;
    std::cout << "  " << std::endl;
    std::cout << "  # 仅下载插画类型" << std::endl;
    std::cout << "  PixivTagDownloader -u 12345678 -T illust --all-works" << std::endl;
}

void CliHandler::ShowVersion() {
    std::cout << "PixivTagDownloader v1.0.0" << std::endl;
    std::cout << "一个高效的Pixiv作品下载器" << std::endl;
    std::cout << "构建时间: " << __DATE__ << " " << __TIME__ << std::endl;
}

void CliHandler::ShowWelcome() {
    std::cout << R"(
 ____  _       _       _____             ____                      _                 _           
|  _ \(_)_  __(_)_   _|_   _|_ _  __ _   |  _ \  _____      ___ __ | | ___   __ _  __| | ___ _ __ 
| |_) | \ \/ /| \ \ / / | |/ _` |/ _` |  | | | |/ _ \ \ /\ / / '_ \| |/ _ \ / _` |/ _` |/ _ \ '__|
|  __/| |>  < | |\ V /  | | (_| | (_| |  | |_| | (_) \ V  V /| | | | | (_) | (_| | (_| |  __/ |   
|_|   |_/_/\_\|_| \_/   |_|\__,_|\__, |  |____/ \___/ \_/\_/ |_| |_|_|\___/ \__,_|\__,_|\___|_|   
                                 |___/                                                            
)" << std::endl;
    
    std::cout << "欢迎使用 PixivTagDownloader!" << std::endl;
    std::cout << "这是一个用于从Pixiv下载作品的工具。" << std::endl;
    std::cout << std::endl;
}

bool CliHandler::ConfirmSelection(const UserSelection& selection) {
    std::cout << "\n=== 确认您的选择 ===" << std::endl;
    std::cout << "用户ID: " << selection.uid << std::endl;
    
    std::cout << "作品类型: ";
    std::vector<std::string> type_names;
    for (const auto& type : selection.artwork_types) {
        type_names.push_back(config::ConfigManager::ArtworkTypeToString(type));
    }
    std::cout << utilities::StringUtils::Join(type_names, ", ") << std::endl;
    
    if (selection.download_all_works) {
        std::cout << "下载方式: 所有作品" << std::endl;
    } else {
        std::cout << "下载方式: 按标签筛选" << std::endl;
        std::cout << "标签: " << utilities::StringUtils::Join(selection.tags, ", ") << std::endl;
        std::cout << "过滤逻辑: " << config::ConfigManager::TagFilterLogicToString(selection.tag_logic) << std::endl;
    }
    
    return GetUserConfirmation("是否继续下载?", true);
}

bool CliHandler::AskForNewTask() {
    std::cout << std::endl;
    try {
        return GetUserConfirmation("是否要下载其他用户的作品?", false);
    } catch (const std::runtime_error& e) {
        // 输入流结束，默认不继续
        std::cout << "输入流结束，程序退出。" << std::endl;
        return false;
    }
}

void CliHandler::SetupProgressCallback(download::DownloaderCore& downloader) {
    downloader_instance_ = &downloader;
    
    downloader.SetProgressCallback([this](const download::DownloadProgress& progress) {
        progress_display_->UpdateProgress(progress);
    });
    
    downloader.SetTaskCompletionCallback([this](const download::DownloadResult& result) {
        progress_display_->ShowTaskCompletion(result);
    });
    
    progress_display_->Start();
}

void CliHandler::SetupSignalHandlers(download::DownloaderCore& downloader) {
    downloader_instance_ = &downloader;
    
    // 设置信号处理器
    std::signal(SIGINT, SignalHandler);
    std::signal(SIGTERM, SignalHandler);
}

void CliHandler::ShowError(const std::string& message) {
    std::cerr << "错误: " << message << std::endl;
}

void CliHandler::ShowWarning(const std::string& message) {
    std::cout << "警告: " << message << std::endl;
}

void CliHandler::ShowInfo(const std::string& message) {
    std::cout << "信息: " << message << std::endl;
}

// 私有方法实现
std::string CliHandler::GetUserInput(const std::string& prompt, const std::string& default_value) {
    std::cout << prompt;
    if (!default_value.empty()) {
        std::cout << " [" << default_value << "]";
    }
    std::cout << ": ";

    std::string input;
    if (!std::getline(std::cin, input)) {
        // 输入流结束或出错，返回默认值或空字符串
        if (!default_value.empty()) {
            std::cout << default_value << std::endl;
            return default_value;
        }
        // 如果没有默认值且输入流结束，抛出异常或返回特殊值
        throw std::runtime_error("输入流已结束");
    }

    input = utilities::StringUtils::Trim(input);
    return input.empty() ? default_value : input;
}

bool CliHandler::GetUserConfirmation(const std::string& prompt, bool default_yes) {
    std::string suffix = default_yes ? " (Y/n)" : " (y/N)";
    std::string input = GetUserInput(prompt + suffix);
    
    if (input.empty()) {
        return default_yes;
    }
    
    std::string lower_input = utilities::StringUtils::ToLower(input);
    return (lower_input == "y" || lower_input == "yes");
}

std::vector<config::ArtworkType> CliHandler::SelectArtworkTypes() {
    std::cout << "\n📚 选择要下载的作品类型：" << std::endl;
    std::cout << "1. 插画 (Illust) - 单张图片作品" << std::endl;
    std::cout << "2. 漫画 (Manga) - 多页漫画作品" << std::endl;
    std::cout << "3. 小说 (Novel) - 文字小说作品" << std::endl;
    std::cout << "4. 全部 (All) - 包含所有类型" << std::endl;
    std::cout << "\n💡 提示：可多选，用逗号分隔，如：1,2" << std::endl;

    std::string input = GetUserInput("请输入选择", "4");

    std::vector<config::ArtworkType> types;
    auto choices = utilities::StringUtils::Split(input, ",");

    for (auto& choice : choices) {
        choice = utilities::StringUtils::Trim(choice);
        if (choice == "1") {
            types.push_back(config::ArtworkType::ILLUST);
            std::cout << "✅ 已选择：插画" << std::endl;
        }
        else if (choice == "2") {
            types.push_back(config::ArtworkType::MANGA);
            std::cout << "✅ 已选择：漫画" << std::endl;
        }
        else if (choice == "3") {
            types.push_back(config::ArtworkType::NOVEL);
            std::cout << "✅ 已选择：小说" << std::endl;
        }
        else if (choice == "4") {
            std::cout << "✅ 已选择：全部类型" << std::endl;
            return {config::ArtworkType::ALL};
        }
    }

    if (types.empty()) {
        std::cout << "⚠️  未选择有效类型，默认选择全部" << std::endl;
        return {config::ArtworkType::ALL};
    }

    return types;
}

std::string CliHandler::InputUserId() {
    std::string uid;
    int attempts = 0;
    const int max_attempts = 3;

    std::cout << "\n💡 用户ID格式说明：" << std::endl;
    std::cout << "   - 用户ID是纯数字，通常为8-9位" << std::endl;
    std::cout << "   - 示例：28925283" << std::endl;
    std::cout << "   - 可以从用户主页URL中获取：https://www.pixiv.net/users/28925283" << std::endl;

    while (attempts < max_attempts) {
        try {
            uid = GetUserInput("\n请输入画师的Pixiv用户ID");

            // 检查输入是否为空
            if (uid.empty()) {
                std::cout << "❌ 用户ID不能为空，请重新输入。" << std::endl;
                attempts++;
                continue;
            }

            // 验证用户ID格式
            if (ValidateUserId(uid)) {
                std::cout << "✅ 用户ID格式正确: " << uid << std::endl;
                return uid;
            } else {
                std::cout << "❌ 无效的用户ID格式。" << std::endl;
                std::cout << "   用户ID应该是纯数字，例如: 28925283" << std::endl;
                attempts++;

                if (attempts < max_attempts) {
                    std::cout << "   剩余尝试次数: " << (max_attempts - attempts) << std::endl;
                }
            }
        } catch (const std::runtime_error& e) {
            std::cout << "❌ 输入错误: " << e.what() << std::endl;
            throw;
        }
    }

    // 达到最大尝试次数
    std::cout << "❌ 输入尝试次数过多，程序退出。" << std::endl;
    throw std::runtime_error("用户ID输入失败");
}

int CliHandler::SelectDownloadMethod() {
    std::cout << "\n🎯 选择下载方式：" << std::endl;
    std::cout << "1. 下载所有作品 - 下载该用户的全部公开作品" << std::endl;
    std::cout << "2. 按手动输入的标签筛选 - 您手动指定要筛选的标签" << std::endl;
    std::cout << "3. 从用户现有作品的标签列表中选择 - 从该用户作品中提取标签供您选择" << std::endl;
    std::cout << "\n💡 提示：" << std::endl;
    std::cout << "   - 方式1适合下载喜欢画师的全部作品" << std::endl;
    std::cout << "   - 方式2适合您已知想要的特定标签" << std::endl;
    std::cout << "   - 方式3适合探索该画师的作品标签" << std::endl;

    std::string input = GetUserInput("请选择", "1");

    int choice = 1;
    try {
        choice = std::stoi(input);
        if (choice < 1 || choice > 3) {
            std::cout << "⚠️  无效选择，使用默认方式：下载所有作品" << std::endl;
            choice = 1;
        } else {
            const char* methods[] = {"", "下载所有作品", "手动输入标签", "从作品中选择标签"};
            std::cout << "✅ 已选择：" << methods[choice] << std::endl;
        }
    } catch (...) {
        std::cout << "⚠️  输入格式错误，使用默认方式：下载所有作品" << std::endl;
        choice = 1;
    }

    return choice;
}

std::vector<std::string> CliHandler::InputTagsManually() {
    std::cout << "\n🏷️  手动输入标签：" << std::endl;
    std::cout << "💡 标签输入说明：" << std::endl;
    std::cout << "   - 多个标签用逗号分隔，如：原创,插画,风景" << std::endl;
    std::cout << "   - 支持中文和英文标签" << std::endl;
    std::cout << "   - 标签名称区分大小写" << std::endl;
    std::cout << "   - 常见标签示例：原创、插画、漫画、R-18、百合、东方Project" << std::endl;

    std::string input = GetUserInput("\n请输入要筛选的标签");

    if (input.empty()) {
        std::cout << "⚠️  未输入任何标签，将返回空列表" << std::endl;
        return {};
    }

    auto tags = utilities::StringUtils::Split(input, ",");
    std::vector<std::string> valid_tags;

    for (auto& tag : tags) {
        tag = utilities::StringUtils::Trim(tag);
        if (!tag.empty()) {
            valid_tags.push_back(tag);
            std::cout << "✅ 已添加标签：" << tag << std::endl;
        }
    }

    if (valid_tags.empty()) {
        std::cout << "⚠️  没有有效的标签，将返回空列表" << std::endl;
    } else {
        std::cout << "📋 共添加 " << valid_tags.size() << " 个标签" << std::endl;
    }

    return valid_tags;
}

std::vector<std::string> CliHandler::SelectTagsFromUser(const std::string& uid) {
    std::cout << "\n正在获取用户标签... (这可能需要一些时间)" << std::endl;

    if (!api_client_) {
        std::cout << "API客户端未初始化，请使用手动输入。" << std::endl;
        return InputTagsManually();
    }

    try {
        // 询问最大检查作品数量
        std::string max_str = GetUserInput("最大检查作品数量 (0=全部，建议100-500)", "200");
        int max_artworks = 0;
        try {
            max_artworks = std::stoi(max_str);
        } catch (...) {
            max_artworks = 200;
        }

        // 获取用户标签
        auto all_tags = api_client_->GetUserTags(uid, max_artworks);

        if (all_tags.empty()) {
            std::cout << "未找到任何标签，请使用手动输入。" << std::endl;
            return InputTagsManually();
        }

        std::cout << "找到 " << all_tags.size() << " 个唯一标签。" << std::endl;

        // 显示标签并让用户选择
        return DisplayTagsWithPagination(all_tags);

    } catch (const std::exception& e) {
        std::cout << "获取标签时发生错误: " << e.what() << std::endl;
        std::cout << "请使用手动输入。" << std::endl;
        return InputTagsManually();
    }
}

config::TagFilterLogic CliHandler::SelectTagFilterLogic() {
    std::cout << "\n🔍 选择标签筛选逻辑：" << std::endl;
    std::cout << "1. AND (交集) - 作品必须包含所有选定标签" << std::endl;
    std::cout << "2. OR (并集) - 作品只需包含任意一个选定标签" << std::endl;
    std::cout << "3. NOT (排除) - 排除包含指定标签的作品" << std::endl;
    std::cout << "\n💡 选择建议：" << std::endl;
    std::cout << "   - AND：适合精确筛选，如同时要求'原创'和'插画'" << std::endl;
    std::cout << "   - OR：适合广泛收集，如包含'风景'或'人物'的作品" << std::endl;
    std::cout << "   - NOT：适合排除不想要的内容，如排除'R-18'标签" << std::endl;

    std::string input = GetUserInput("请选择", "2");

    int choice = 2;
    try {
        choice = std::stoi(input);
        if (choice < 1 || choice > 3) {
            std::cout << "⚠️  无效选择，使用默认逻辑：OR" << std::endl;
            choice = 2;
        }
    } catch (...) {
        std::cout << "⚠️  输入格式错误，使用默认逻辑：OR" << std::endl;
        choice = 2;
    }

    const char* logic_names[] = {"", "AND (交集)", "OR (并集)", "NOT (排除)"};
    std::cout << "✅ 已选择筛选逻辑：" << logic_names[choice] << std::endl;

    switch (choice) {
        case 1: return config::TagFilterLogic::AND;
        case 3: return config::TagFilterLogic::NOT;
        default: return config::TagFilterLogic::OR;
    }
}

std::vector<std::string> CliHandler::DisplayTagsWithPagination(const std::vector<std::string>& tags, int page_size) {
    std::vector<std::string> selected_tags;

    if (tags.empty()) {
        return selected_tags;
    }

    int total_pages = (tags.size() + page_size - 1) / page_size;
    int current_page = 0;

    while (true) {
        // 显示当前页的标签
        std::cout << "\n=== 标签列表 (第 " << (current_page + 1) << "/" << total_pages << " 页) ===" << std::endl;

        int start_idx = current_page * page_size;
        int end_idx = std::min(start_idx + page_size, static_cast<int>(tags.size()));

        for (int i = start_idx; i < end_idx; ++i) {
            std::cout << std::setw(3) << (i + 1) << ". " << tags[i] << std::endl;
        }

        std::cout << "\n操作选项:" << std::endl;
        std::cout << "  输入标签编号选择 (用逗号分隔多个，如: 1,3,5)" << std::endl;
        std::cout << "  输入 'n' 下一页，'p' 上一页" << std::endl;
        std::cout << "  输入 's' 搜索标签" << std::endl;
        std::cout << "  输入 'done' 完成选择" << std::endl;
        std::cout << "  输入 'manual' 手动输入标签" << std::endl;

        if (!selected_tags.empty()) {
            std::cout << "\n已选择的标签: " << utilities::StringUtils::Join(selected_tags, ", ") << std::endl;
        }

        std::string input = GetUserInput("请选择");
        input = utilities::StringUtils::Trim(input);

        if (input == "done") {
            break;
        } else if (input == "manual") {
            auto manual_tags = InputTagsManually();
            selected_tags.insert(selected_tags.end(), manual_tags.begin(), manual_tags.end());
        } else if (input == "n" && current_page < total_pages - 1) {
            current_page++;
        } else if (input == "p" && current_page > 0) {
            current_page--;
        } else if (input == "s") {
            std::string keyword = GetUserInput("输入搜索关键词");
            auto search_results = SearchTags(tags, keyword);
            if (!search_results.empty()) {
                auto search_selected = DisplayTagsWithPagination(search_results, page_size);
                selected_tags.insert(selected_tags.end(), search_selected.begin(), search_selected.end());
            } else {
                std::cout << "未找到匹配的标签。" << std::endl;
            }
        } else {
            // 解析标签编号
            auto choices = utilities::StringUtils::Split(input, ",");
            for (auto& choice : choices) {
                choice = utilities::StringUtils::Trim(choice);
                try {
                    int idx = std::stoi(choice) - 1;
                    if (idx >= start_idx && idx < end_idx) {
                        const std::string& tag = tags[idx];
                        if (std::find(selected_tags.begin(), selected_tags.end(), tag) == selected_tags.end()) {
                            selected_tags.push_back(tag);
                            std::cout << "已添加标签: " << tag << std::endl;
                        } else {
                            std::cout << "标签已存在: " << tag << std::endl;
                        }
                    } else {
                        std::cout << "无效的标签编号: " << choice << std::endl;
                    }
                } catch (...) {
                    std::cout << "无效的输入: " << choice << std::endl;
                }
            }
        }
    }

    return selected_tags;
}

std::vector<std::string> CliHandler::SearchTags(const std::vector<std::string>& tags, const std::string& keyword) {
    std::vector<std::string> results;
    std::string lower_keyword = utilities::StringUtils::ToLower(keyword);

    for (const auto& tag : tags) {
        std::string lower_tag = utilities::StringUtils::ToLower(tag);
        if (lower_tag.find(lower_keyword) != std::string::npos) {
            results.push_back(tag);
        }
    }

    return results;
}

bool CliHandler::ValidateUserId(const std::string& uid) {
    return !uid.empty() && utilities::StringUtils::IsNumeric(uid);
}

void CliHandler::SignalHandler(int /*signal*/) {
    if (instance_) {
        instance_->signal_received_ = true;
        instance_->signal_count_++;
        
        if (instance_->signal_count_ == 1) {
            std::cout << "\n检测到 Ctrl-C。正在尝试优雅关闭..." << std::endl;
            if (downloader_instance_) {
                downloader_instance_->Stop(true);
            }
        } else if (instance_->signal_count_ >= 2) {
            std::cout << "\n强制退出程序。" << std::endl;
            std::exit(1);
        }
    }
}

// ProgressDisplay 实现
ProgressDisplay::ProgressDisplay() : is_running_(false), verbose_(false) {
}

ProgressDisplay::~ProgressDisplay() {
    Stop();
}

void ProgressDisplay::Start() {
    is_running_ = true;
}

void ProgressDisplay::Stop() {
    is_running_ = false;
}

void ProgressDisplay::UpdateProgress(const download::DownloadProgress& progress) {
    if (!is_running_) return;
    
    std::lock_guard<std::mutex> lock(display_mutex_);
    
    // 清除当前行
    ClearCurrentLine();
    
    // 显示总体进度
    std::cout << "进度: " << progress.completed_tasks << "/" << progress.total_tasks 
              << " (" << std::fixed << std::setprecision(1) << progress.overall_progress << "%)";
    
    if (!progress.current_file.empty()) {
        std::cout << " - " << progress.current_file;
        if (progress.current_file_progress > 0) {
            std::cout << " (" << std::fixed << std::setprecision(1) 
                      << progress.current_file_progress << "%)";
        }
    }
    
    std::cout << std::flush;
}

void ProgressDisplay::ShowTaskCompletion(const download::DownloadResult& result) {
    if (!verbose_) return;
    
    std::lock_guard<std::mutex> lock(display_mutex_);
    
    ClearCurrentLine();
    
    switch (result.status) {
        case download::DownloadStatus::COMPLETED:
            std::cout << "✓ 完成: " << result.file_path << std::endl;
            break;
        case download::DownloadStatus::FAILED:
            std::cout << "✗ 失败: " << result.file_path 
                      << " (" << result.error_message << ")" << std::endl;
            break;
        case download::DownloadStatus::SKIPPED:
            std::cout << "- 跳过: " << result.file_path << std::endl;
            break;
        default:
            break;
    }
}

void ProgressDisplay::ShowFinalStatistics(const std::vector<download::DownloadResult>& results) {
    std::lock_guard<std::mutex> lock(display_mutex_);
    
    ClearCurrentLine();
    
    int completed = 0, failed = 0, skipped = 0;
    size_t total_bytes = 0;
    
    for (const auto& result : results) {
        switch (result.status) {
            case download::DownloadStatus::COMPLETED:
                completed++;
                total_bytes += result.file_size;
                break;
            case download::DownloadStatus::FAILED:
                failed++;
                break;
            case download::DownloadStatus::SKIPPED:
                skipped++;
                break;
            default:
                break;
        }
    }
    
    std::cout << "\n下载统计:" << std::endl;
    std::cout << "成功: " << completed << std::endl;
    std::cout << "失败: " << failed << std::endl;
    std::cout << "跳过: " << skipped << std::endl;
    std::cout << "总大小: " << FormatFileSize(total_bytes) << std::endl;
}

void ProgressDisplay::ClearCurrentLine() {
    std::cout << "\r\033[K";
}

std::string ProgressDisplay::FormatFileSize(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024.0 && unit < 4) {
        size /= 1024.0;
        unit++;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit];
    return oss.str();
}

} // namespace cli
} // namespace pixiv_downloader
