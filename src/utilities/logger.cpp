#include "utilities/logger.h"
#include <spdlog/async.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <iostream>

namespace pixiv_downloader {
namespace utilities {

// 静态成员初始化
std::shared_ptr<spdlog::logger> Logger::logger_ = nullptr;
bool Logger::initialized_ = false;

bool Logger::Initialize(Level log_level, const std::string& log_file_path, 
                       size_t max_file_size, size_t max_files) {
    try {
        // 如果已经初始化，先关闭
        if (initialized_) {
            Shutdown();
        }

        std::vector<spdlog::sink_ptr> sinks;

        // 控制台输出
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(static_cast<spdlog::level::level_enum>(log_level));
        console_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%s:%#] %v");
        sinks.push_back(console_sink);

        // 文件输出（如果指定了路径）
        if (!log_file_path.empty()) {
            std::string full_log_path = log_file_path;
            if (log_file_path.back() != '/' && log_file_path.back() != '\\') {
                full_log_path += "/";
            }
            full_log_path += GenerateLogFileName("");

            auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                full_log_path, max_file_size, max_files);
            file_sink->set_level(static_cast<spdlog::level::level_enum>(log_level));
            file_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%s:%#] %v");
            sinks.push_back(file_sink);
        }

        // 创建异步日志器
        spdlog::init_thread_pool(8192, 1);
        logger_ = std::make_shared<spdlog::async_logger>(
            "PixivTagDownloader", sinks.begin(), sinks.end(),
            spdlog::thread_pool(), spdlog::async_overflow_policy::block);

        logger_->set_level(static_cast<spdlog::level::level_enum>(log_level));
        logger_->flush_on(spdlog::level::warn);

        // 设置为默认日志器
        spdlog::set_default_logger(logger_);

        initialized_ = true;
        LOG_INFO("日志系统初始化成功");
        return true;

    } catch (const std::exception& e) {
        std::cerr << "日志系统初始化失败: " << e.what() << std::endl;
        return false;
    }
}

std::shared_ptr<spdlog::logger> Logger::GetLogger() {
    if (!initialized_ || !logger_) {
        // 如果未初始化，创建一个基本的控制台日志器
        if (!logger_) {
            logger_ = spdlog::stdout_color_mt("PixivTagDownloader");
            logger_->set_level(spdlog::level::info);
        }
    }
    return logger_;
}

void Logger::SetLevel(Level level) {
    if (logger_) {
        logger_->set_level(static_cast<spdlog::level::level_enum>(level));
    }
}

void Logger::Shutdown() {
    if (initialized_ && logger_) {
        logger_->flush();
        spdlog::shutdown();
        logger_ = nullptr;
        initialized_ = false;
    }
}

Logger::Level Logger::StringToLevel(const std::string& level_str) {
    std::string lower_str = level_str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);

    if (lower_str == "trace") return Level::TRACE;
    if (lower_str == "debug") return Level::DEBUG;
    if (lower_str == "info") return Level::INFO;
    if (lower_str == "warn" || lower_str == "warning") return Level::WARN;
    if (lower_str == "error") return Level::ERROR;
    if (lower_str == "fatal" || lower_str == "critical") return Level::FATAL;

    return Level::INFO; // 默认级别
}

std::string Logger::LevelToString(Level level) {
    switch (level) {
        case Level::TRACE: return "trace";
        case Level::DEBUG: return "debug";
        case Level::INFO: return "info";
        case Level::WARN: return "warn";
        case Level::ERROR: return "error";
        case Level::FATAL: return "fatal";
        default: return "info";
    }
}

std::string Logger::GenerateLogFileName(const std::string& base_path) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    std::ostringstream oss;
    if (!base_path.empty()) {
        oss << base_path;
        if (base_path.back() != '/' && base_path.back() != '\\') {
            oss << "/";
        }
    }
    
    oss << std::put_time(&tm, "%Y-%m-%d_%H-%M-%S") << ".log";
    return oss.str();
}

} // namespace utilities
} // namespace pixiv_downloader
