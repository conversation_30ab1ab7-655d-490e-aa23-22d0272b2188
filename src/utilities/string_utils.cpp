#include "utilities/string_utils.h"
#include <regex>
#include <iomanip>
#include <sstream>
#include <functional>

namespace pixiv_downloader {
namespace utilities {

// 文件系统非法字符定义
const std::string StringUtils::ILLEGAL_FILENAME_CHARS = "<>:\"/\\|?*";

std::string StringUtils::Trim(const std::string& str) {
    return TrimLeft(TrimRight(str));
}

std::string StringUtils::TrimLeft(const std::string& str) {
    auto start = str.begin();
    while (start != str.end() && std::isspace(*start)) {
        start++;
    }
    return std::string(start, str.end());
}

std::string StringUtils::TrimRight(const std::string& str) {
    auto end = str.end();
    while (end != str.begin() && std::isspace(*(end - 1))) {
        end--;
    }
    return std::string(str.begin(), end);
}

std::string StringUtils::ToLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string StringUtils::ToUpper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::vector<std::string> StringUtils::Split(const std::string& str, const std::string& delimiter) {
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = str.find(delimiter);

    while (end != std::string::npos) {
        std::string token = str.substr(start, end - start);
        if (!token.empty()) {
            tokens.push_back(token);
        }
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }

    std::string last_token = str.substr(start);
    if (!last_token.empty()) {
        tokens.push_back(last_token);
    }

    return tokens;
}

std::string StringUtils::Join(const std::vector<std::string>& strings, const std::string& delimiter) {
    if (strings.empty()) {
        return "";
    }

    std::ostringstream oss;
    for (size_t i = 0; i < strings.size(); ++i) {
        if (i > 0) {
            oss << delimiter;
        }
        oss << strings[i];
    }
    return oss.str();
}

std::string StringUtils::ReplaceAll(const std::string& str, const std::string& from, const std::string& to) {
    if (from.empty()) {
        return str;
    }

    std::string result = str;
    size_t pos = 0;
    while ((pos = result.find(from, pos)) != std::string::npos) {
        result.replace(pos, from.length(), to);
        pos += to.length();
    }
    return result;
}

bool StringUtils::StartsWith(const std::string& str, const std::string& prefix) {
    if (prefix.length() > str.length()) {
        return false;
    }
    return str.substr(0, prefix.length()) == prefix;
}

bool StringUtils::EndsWith(const std::string& str, const std::string& suffix) {
    if (suffix.length() > str.length()) {
        return false;
    }
    return str.substr(str.length() - suffix.length()) == suffix;
}

std::string StringUtils::SanitizeFileName(const std::string& str, const std::string& replacement) {
    std::string result = str;
    
    // 替换非法字符
    for (char illegal_char : ILLEGAL_FILENAME_CHARS) {
        result = ReplaceAll(result, std::string(1, illegal_char), replacement);
    }
    
    // 移除控制字符
    result.erase(std::remove_if(result.begin(), result.end(), 
        [](unsigned char c) { return c < 32 || c == 127; }), result.end());
    
    // 处理Windows保留名称
    std::vector<std::string> reserved_names = {
        "CON", "PRN", "AUX", "NUL",
        "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
        "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"
    };
    
    std::string upper_result = ToUpper(result);
    for (const auto& reserved : reserved_names) {
        if (upper_result == reserved || StartsWith(upper_result, reserved + ".")) {
            result = replacement + result;
            break;
        }
    }
    
    // 移除开头和结尾的点和空格
    result = Trim(result);
    while (!result.empty() && (result.front() == '.' || result.front() == ' ')) {
        result.erase(0, 1);
    }
    while (!result.empty() && (result.back() == '.' || result.back() == ' ')) {
        result.pop_back();
    }
    
    // 如果结果为空，提供默认名称
    if (result.empty()) {
        result = "unknown_user";
    }
    
    return result;
}

std::string StringUtils::Truncate(const std::string& str, size_t max_length, const std::string& suffix) {
    if (str.length() <= max_length) {
        return str;
    }
    
    if (max_length <= suffix.length()) {
        return str.substr(0, max_length);
    }
    
    return str.substr(0, max_length - suffix.length()) + suffix;
}

std::string StringUtils::GenerateHash(const std::string& str) {
    std::hash<std::string> hasher;
    size_t hash_value = hasher(str);
    
    std::ostringstream oss;
    oss << std::hex << hash_value;
    return oss.str();
}

std::string StringUtils::UrlEncode(const std::string& str) {
    std::ostringstream encoded;
    encoded.fill('0');
    encoded << std::hex;

    for (char c : str) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            encoded << c;
        } else {
            encoded << std::uppercase;
            encoded << '%' << std::setw(2) << static_cast<int>(static_cast<unsigned char>(c));
            encoded << std::nouppercase;
        }
    }

    return encoded.str();
}

std::string StringUtils::UrlDecode(const std::string& str) {
    std::string decoded;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            std::string hex = str.substr(i + 1, 2);
            char c = static_cast<char>(std::stoi(hex, nullptr, 16));
            decoded += c;
            i += 2;
        } else if (str[i] == '+') {
            decoded += ' ';
        } else {
            decoded += str[i];
        }
    }
    return decoded;
}

bool StringUtils::IsNumeric(const std::string& str) {
    if (str.empty()) {
        return false;
    }
    
    return std::all_of(str.begin(), str.end(), ::isdigit);
}

} // namespace utilities
} // namespace pixiv_downloader
