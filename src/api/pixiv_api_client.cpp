#include "api/pixiv_api_client.h"
#include "utilities/logger.h"
#include "utilities/string_utils.h"
#include "utilities/file_utils.h"
#include <curl/curl.h>
#include <fstream>
#include <thread>
#include <random>
#include <set>
#include <algorithm>
#include <mutex>

namespace pixiv_downloader {
namespace api {

// 静态常量定义
const std::string PixivApiClient::PIXIV_BASE_URL = "https://www.pixiv.net";
const std::string PixivApiClient::PIXIV_API_BASE_URL = "https://www.pixiv.net/ajax";
const std::string PixivApiClient::USER_AGENT_DEFAULT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
const std::string PixivApiClient::REFERER_DEFAULT = "https://www.pixiv.net/";

// 全局CURL初始化管理
static bool curl_global_initialized = false;
static std::mutex curl_global_mutex;

PixivApiClient::PixivApiClient(const config::Config& config, auth::PixivAuth& auth)
    : config_(config), auth_(auth), curl_handle_(nullptr),
      min_delay_ms_(config.delay.min_delay * 1000),
      max_delay_ms_(config.delay.max_delay * 1000) {
}

PixivApiClient::~PixivApiClient() {
    Cleanup();
}

bool PixivApiClient::Initialize() {
    LOG_DEBUG("开始初始化API客户端...");

    // 安全地初始化libcurl全局状态
    {
        std::lock_guard<std::mutex> lock(curl_global_mutex);
        if (!curl_global_initialized) {
            CURLcode global_init_result = curl_global_init(CURL_GLOBAL_DEFAULT);
            if (global_init_result != CURLE_OK) {
                LOG_ERROR("CURL全局初始化失败: {}", curl_easy_strerror(global_init_result));
                return false;
            }
            curl_global_initialized = true;
            LOG_DEBUG("CURL全局初始化成功");
        }
    }

    curl_handle_ = curl_easy_init();
    if (!curl_handle_) {
        LOG_ERROR("初始化CURL句柄失败");
        return false;
    }

    // 设置基本选项
    SetupCurlOptions();

    LOG_INFO("API客户端初始化成功");
    return true;
}

void PixivApiClient::Cleanup() {
    if (curl_handle_) {
        curl_easy_cleanup(curl_handle_);
        curl_handle_ = nullptr;
        LOG_DEBUG("CURL句柄已清理");
    }

    // 注意：不在这里调用curl_global_cleanup()，因为可能有其他实例在使用
    // CURL全局清理应该在程序退出时进行
}

UserInfo PixivApiClient::GetUserInfo(const std::string& uid) {
    UserInfo user_info;
    
    try {
        std::string url = BuildApiUrl("/user/" + uid);
        auto response = PerformGetRequest(url);
        
        if (!IsResponseSuccessful(response)) {
            LOG_ERROR("获取用户信息失败: {}", HandleApiError(response));
            return user_info;
        }
        
        auto json_data = ParseJsonResponse(response);
        if (json_data.contains("body")) {
            const auto& body = json_data["body"];
            user_info.uid = uid;
            if (body.contains("name")) {
                user_info.username = body["name"].get<std::string>();
            }
            if (body.contains("imageBig")) {
                user_info.profile_image_url = body["imageBig"].get<std::string>();
            }
            if (body.contains("comment")) {
                user_info.comment = body["comment"].get<std::string>();
            }
        }
        
        LOG_INFO("成功获取用户信息: {} ({})", user_info.username, uid);
        
    } catch (const std::exception& e) {
        LOG_ERROR("获取用户信息时发生异常: {}", e.what());
        last_error_ = e.what();
    }
    
    ApplyRequestDelay();
    return user_info;
}

std::map<std::string, std::vector<std::string>> PixivApiClient::GetUserArtworkIds(const std::string& uid) {
    std::map<std::string, std::vector<std::string>> artwork_ids;
    
    try {
        std::string url = BuildApiUrl("/user/" + uid + "/profile/all");
        auto response = PerformGetRequest(url);
        
        if (!IsResponseSuccessful(response)) {
            LOG_ERROR("获取用户作品ID列表失败: {}", HandleApiError(response));
            return artwork_ids;
        }
        
        auto json_data = ParseJsonResponse(response);
        if (json_data.contains("body")) {
            const auto& body = json_data["body"];
            
            // 处理插画和漫画
            if (body.contains("illusts")) {
                for (const auto& [pid, _] : body["illusts"].items()) {
                    artwork_ids["illustrations"].push_back(pid);
                }
            }
            
            // 处理小说
            if (body.contains("novels")) {
                for (const auto& [pid, _] : body["novels"].items()) {
                    artwork_ids["novels"].push_back(pid);
                }
            }
        }
        
        LOG_INFO("获取到用户作品ID: 插画/漫画 {}, 小说 {}", 
                artwork_ids["illustrations"].size(), artwork_ids["novels"].size());
        
    } catch (const std::exception& e) {
        LOG_ERROR("获取用户作品ID时发生异常: {}", e.what());
        last_error_ = e.what();
    }
    
    ApplyRequestDelay();
    return artwork_ids;
}

ArtworkInfo PixivApiClient::GetArtworkDetails(const std::string& pid) {
    ArtworkInfo artwork;
    
    try {
        std::string url = BuildApiUrl("/illust/" + pid);
        auto response = PerformGetRequest(url);
        
        if (!IsResponseSuccessful(response)) {
            LOG_ERROR("获取作品详情失败: {}", HandleApiError(response));
            return artwork;
        }
        
        auto json_data = ParseJsonResponse(response);
        if (json_data.contains("body")) {
            const auto& body = json_data["body"];
            
            artwork.pid = pid;
            if (body.contains("title")) {
                artwork.title = body["title"].get<std::string>();
            }
            if (body.contains("description")) {
                artwork.description = body["description"].get<std::string>();
            }
            if (body.contains("userId")) {
                artwork.author_uid = body["userId"].get<std::string>();
            }
            if (body.contains("userName")) {
                artwork.author_username = body["userName"].get<std::string>();
            }
            if (body.contains("pageCount")) {
                artwork.page_count = body["pageCount"].get<int>();
            }
            if (body.contains("likeCount")) {
                artwork.like_count = body["likeCount"].get<int>();
            }
            if (body.contains("bookmarkCount")) {
                artwork.bookmark_count = body["bookmarkCount"].get<int>();
            }
            if (body.contains("createDate")) {
                artwork.upload_date = body["createDate"].get<std::string>();
            }
            
            // 处理标签
            if (body.contains("tags") && body["tags"].contains("tags")) {
                for (const auto& tag : body["tags"]["tags"]) {
                    if (tag.contains("tag")) {
                        artwork.tags.push_back(tag["tag"].get<std::string>());
                    }
                }
            }
            
            // 判断作品类型
            if (body.contains("illustType")) {
                int illust_type = body["illustType"].get<int>();
                artwork.type = (illust_type == 2) ? config::ArtworkType::MANGA : config::ArtworkType::ILLUST;
            }
            
            // 检查R18
            if (body.contains("sl")) {
                artwork.is_r18 = (body["sl"].get<int>() >= 4);
            }
            
            // 获取图片URL
            if (body.contains("urls")) {
                const auto& urls = body["urls"];
                if (urls.contains("original")) {
                    artwork.image_urls.push_back(urls["original"].get<std::string>());
                }
            }
            
            // 如果是多页作品，获取所有页面
            if (artwork.page_count > 1) {
                auto page_urls = GetArtworkPages(pid);
                artwork.image_urls = page_urls;
            }
        }
        
        LOG_DEBUG("获取作品详情成功: {} - {}", pid, artwork.title);
        
    } catch (const std::exception& e) {
        LOG_ERROR("获取作品详情时发生异常: {}", e.what());
        last_error_ = e.what();
    }
    
    ApplyRequestDelay();
    return artwork;
}

std::vector<std::string> PixivApiClient::GetArtworkPages(const std::string& pid) {
    std::vector<std::string> page_urls;
    
    try {
        std::string url = BuildApiUrl("/illust/" + pid + "/pages");
        auto response = PerformGetRequest(url);
        
        if (!IsResponseSuccessful(response)) {
            LOG_ERROR("获取作品分页失败: {}", HandleApiError(response));
            return page_urls;
        }
        
        auto json_data = ParseJsonResponse(response);
        if (json_data.contains("body")) {
            for (const auto& page : json_data["body"]) {
                if (page.contains("urls") && page["urls"].contains("original")) {
                    page_urls.push_back(page["urls"]["original"].get<std::string>());
                }
            }
        }
        
        LOG_DEBUG("获取作品分页成功: {} 页", page_urls.size());
        
    } catch (const std::exception& e) {
        LOG_ERROR("获取作品分页时发生异常: {}", e.what());
        last_error_ = e.what();
    }
    
    ApplyRequestDelay();
    return page_urls;
}

ArtworkInfo PixivApiClient::GetNovelDetails(const std::string& pid) {
    ArtworkInfo novel;
    
    try {
        // 获取小说元数据
        std::string meta_url = BuildApiUrl("/novel/" + pid);
        auto meta_response = PerformGetRequest(meta_url);
        
        if (!IsResponseSuccessful(meta_response)) {
            LOG_ERROR("获取小说元数据失败: {}", HandleApiError(meta_response));
            return novel;
        }
        
        auto meta_json = ParseJsonResponse(meta_response);
        if (meta_json.contains("body")) {
            const auto& body = meta_json["body"];
            
            novel.pid = pid;
            novel.type = config::ArtworkType::NOVEL;
            
            if (body.contains("title")) {
                novel.title = body["title"].get<std::string>();
            }
            if (body.contains("description")) {
                novel.description = body["description"].get<std::string>();
            }
            if (body.contains("userId")) {
                novel.author_uid = body["userId"].get<std::string>();
            }
            if (body.contains("userName")) {
                novel.author_username = body["userName"].get<std::string>();
            }
            if (body.contains("textCount")) {
                novel.word_count = body["textCount"].get<int>();
            }
            if (body.contains("likeCount")) {
                novel.like_count = body["likeCount"].get<int>();
            }
            if (body.contains("bookmarkCount")) {
                novel.bookmark_count = body["bookmarkCount"].get<int>();
            }
            if (body.contains("createDate")) {
                novel.upload_date = body["createDate"].get<std::string>();
            }
            
            // 处理标签
            if (body.contains("tags") && body["tags"].contains("tags")) {
                for (const auto& tag : body["tags"]["tags"]) {
                    if (tag.contains("tag")) {
                        novel.tags.push_back(tag["tag"].get<std::string>());
                    }
                }
            }
            
            // 检查R18
            if (body.contains("xRestrict")) {
                novel.is_r18 = (body["xRestrict"].get<int>() >= 1);
            }
        }
        
        // 小说的内容通常包含在元数据响应中的content字段
        if (meta_json.contains("body") && meta_json["body"].contains("content")) {
            novel.content = meta_json["body"]["content"].get<std::string>();
            LOG_DEBUG("小说内容获取成功，长度: {} 字符", novel.content.length());
        } else {
            LOG_WARN("小说元数据中未找到content字段");
        }
        
        LOG_DEBUG("获取小说详情成功: {} - {}", pid, novel.title);
        
    } catch (const std::exception& e) {
        LOG_ERROR("获取小说详情时发生异常: {}", e.what());
        last_error_ = e.what();
    }
    
    ApplyRequestDelay();
    return novel;
}

bool PixivApiClient::DownloadFile(const std::string& url, const std::string& output_path,
                                 api::ProgressCallback progress_callback) {
    try {
        // 创建输出目录
        std::string dir = utilities::FileUtils::GetDirectoryPath(output_path);
        if (!utilities::FileUtils::DirectoryExists(dir)) {
            utilities::FileUtils::CreateDirectory(dir);
        }
        
        // 打开输出文件
        FILE* file = fopen(output_path.c_str(), "wb");
        if (!file) {
            LOG_ERROR("无法创建输出文件: {}", output_path);
            return false;
        }
        
        // 设置CURL选项
        CURL* curl = curl_easy_init();
        if (!curl) {
            fclose(file);
            return false;
        }
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteFileCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, file);
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_USERAGENT, config_.http.user_agent.c_str());
        curl_easy_setopt(curl, CURLOPT_REFERER, REFERER_DEFAULT.c_str());
        curl_easy_setopt(curl, CURLOPT_COOKIE, auth_.GetCookieString().c_str());
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, config_.http.read_timeout);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, config_.http.connection_timeout);
        
        // 设置进度回调
        if (progress_callback) {
            curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);
            curl_easy_setopt(curl, CURLOPT_XFERINFOFUNCTION, ProgressCallback);
            curl_easy_setopt(curl, CURLOPT_XFERINFODATA, &progress_callback);
        }
        
        // 执行下载
        CURLcode res = curl_easy_perform(curl);
        long response_code = 0;
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);
        
        curl_easy_cleanup(curl);
        fclose(file);
        
        if (res != CURLE_OK || response_code != 200) {
            LOG_ERROR("下载文件失败: {} (CURL: {}, HTTP: {})", url, curl_easy_strerror(res), response_code);
            utilities::FileUtils::DeleteFile(output_path);
            return false;
        }
        
        LOG_DEBUG("文件下载成功: {}", output_path);
        ApplyRequestDelay();
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR("下载文件时发生异常: {}", e.what());
        return false;
    }
}

void PixivApiClient::SetRequestDelay(int min_delay, int max_delay) {
    min_delay_ms_ = min_delay;
    max_delay_ms_ = max_delay;
}

void PixivApiClient::ApplyRequestDelay() {
    if (min_delay_ms_ <= 0 && max_delay_ms_ <= 0) {
        return;
    }

    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_request_time_).count();

    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(min_delay_ms_, max_delay_ms_);
    int delay_ms = dis(gen);

    if (elapsed < delay_ms) {
        std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms - elapsed));
    }

    last_request_time_ = std::chrono::steady_clock::now();
}

std::string PixivApiClient::GetCookieString() const {
    return auth_.GetCookieString();
}

std::vector<std::string> PixivApiClient::GetUserTags(const std::string& uid, int max_artworks) {
    std::vector<std::string> unique_tags;
    std::set<std::string> tag_set; // 用于去重

    try {
        LOG_INFO("开始获取用户 {} 的标签列表", uid);

        // 获取用户所有作品ID
        auto artwork_ids = GetUserArtworkIds(uid);

        int processed_count = 0;
        int total_artworks = 0;

        // 处理插画和漫画
        if (artwork_ids.contains("illustrations")) {
            total_artworks += artwork_ids["illustrations"].size();
            for (const auto& pid : artwork_ids["illustrations"]) {
                if (max_artworks > 0 && processed_count >= max_artworks) {
                    break;
                }

                auto artwork = GetArtworkDetails(pid);
                if (!artwork.pid.empty()) {
                    for (const auto& tag : artwork.tags) {
                        if (!tag.empty()) {
                            tag_set.insert(tag);
                        }
                    }
                }
                processed_count++;

                // 显示进度
                if (processed_count % 10 == 0) {
                    LOG_DEBUG("已处理 {} 个作品，找到 {} 个唯一标签", processed_count, tag_set.size());
                }
            }
        }

        // 处理小说
        if (artwork_ids.contains("novels")) {
            total_artworks += artwork_ids["novels"].size();
            for (const auto& pid : artwork_ids["novels"]) {
                if (max_artworks > 0 && processed_count >= max_artworks) {
                    break;
                }

                auto novel = GetNovelDetails(pid);
                if (!novel.pid.empty()) {
                    for (const auto& tag : novel.tags) {
                        if (!tag.empty()) {
                            tag_set.insert(tag);
                        }
                    }
                }
                processed_count++;

                // 显示进度
                if (processed_count % 10 == 0) {
                    LOG_DEBUG("已处理 {} 个作品，找到 {} 个唯一标签", processed_count, tag_set.size());
                }
            }
        }

        // 转换为vector并排序
        unique_tags.assign(tag_set.begin(), tag_set.end());
        std::sort(unique_tags.begin(), unique_tags.end());

        LOG_INFO("用户 {} 标签提取完成：处理了 {} 个作品，找到 {} 个唯一标签",
                uid, processed_count, unique_tags.size());

    } catch (const std::exception& e) {
        LOG_ERROR("获取用户标签时发生异常: {}", e.what());
        last_error_ = e.what();
    }

    return unique_tags;
}

// 私有方法实现
HttpResponse PixivApiClient::PerformGetRequest(const std::string& url, 
                                              const std::map<std::string, std::string>& headers) {
    HttpResponse response;
    
    if (!curl_handle_) {
        response.error_message = "CURL未初始化";
        return response;
    }
    
    SetupCurlOptions(headers);
    
    std::string response_data;
    curl_easy_setopt(curl_handle_, CURLOPT_URL, url.c_str());
    curl_easy_setopt(curl_handle_, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl_handle_, CURLOPT_WRITEDATA, &response_data);
    
    CURLcode res = curl_easy_perform(curl_handle_);
    curl_easy_getinfo(curl_handle_, CURLINFO_RESPONSE_CODE, &response.status_code);
    
    response.body = response_data;
    response.success = (res == CURLE_OK && response.status_code == 200);
    
    if (res != CURLE_OK) {
        response.error_message = curl_easy_strerror(res);
    }
    
    return response;
}

void PixivApiClient::SetupCurlOptions(const std::map<std::string, std::string>& headers) {
    (void)headers; // 避免未使用参数警告

    if (!curl_handle_) return;

    curl_easy_setopt(curl_handle_, CURLOPT_USERAGENT, config_.http.user_agent.c_str());
    curl_easy_setopt(curl_handle_, CURLOPT_REFERER, config_.http.referer.c_str());
    curl_easy_setopt(curl_handle_, CURLOPT_COOKIE, auth_.GetCookieString().c_str());
    curl_easy_setopt(curl_handle_, CURLOPT_FOLLOWLOCATION, 1L);
    curl_easy_setopt(curl_handle_, CURLOPT_TIMEOUT, config_.http.read_timeout);
    curl_easy_setopt(curl_handle_, CURLOPT_CONNECTTIMEOUT, config_.http.connection_timeout);
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYPEER, 1L);
    curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYHOST, 2L);
}

nlohmann::json PixivApiClient::ParseJsonResponse(const HttpResponse& response) {
    try {
        return nlohmann::json::parse(response.body);
    } catch (const nlohmann::json::exception& e) {
        LOG_ERROR("JSON解析失败: {}", e.what());
        throw;
    }
}

std::string PixivApiClient::BuildApiUrl(const std::string& endpoint) {
    return PIXIV_API_BASE_URL + endpoint;
}

bool PixivApiClient::IsResponseSuccessful(const HttpResponse& response) {
    return response.success && response.status_code == 200;
}

std::string PixivApiClient::HandleApiError(const HttpResponse& response) {
    return "HTTP " + std::to_string(response.status_code) + ": " + response.error_message;
}

// 静态回调函数
size_t PixivApiClient::WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
    size_t total_size = size * nmemb;
    userp->append(static_cast<char*>(contents), total_size);
    return total_size;
}

size_t PixivApiClient::WriteFileCallback(void* contents, size_t size, size_t nmemb, FILE* file) {
    return fwrite(contents, size, nmemb, file);
}

int PixivApiClient::ProgressCallback(void* clientp, curl_off_t dltotal, curl_off_t dlnow,
                                    curl_off_t ultotal, curl_off_t ulnow) {
    (void)ultotal; // 避免未使用参数警告
    (void)ulnow;   // 避免未使用参数警告

    if (clientp && dltotal > 0) {
        auto* callback = static_cast<api::ProgressCallback*>(clientp);
        return (*callback)(static_cast<size_t>(dlnow), static_cast<size_t>(dltotal)) ? 0 : 1;
    }
    return 0;
}

} // namespace api
} // namespace pixiv_downloader
