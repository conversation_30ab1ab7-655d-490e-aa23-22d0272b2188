#include "core/main_controller.h"
#include "utilities/logger.h"
#include "utilities/file_utils.h"
#include "utilities/string_utils.h"
#include <iostream>
#include <iomanip>
#include <thread>

namespace pixiv_downloader {
namespace core {

MainController::MainController() 
    : app_state_(AppState::INITIALIZING), initialized_(false) {
}

MainController::~MainController() {
    Shutdown();
}

ExitCode MainController::Run(int argc, char* argv[]) {
    try {
        SetState(AppState::INITIALIZING);
        
        // 创建配置管理器
        config_manager_ = std::make_unique<config::ConfigManager>();
        
        // 解析命令行参数
        cli_handler_ = std::make_unique<cli::CliHandler>(*config_manager_);
        auto args = cli_handler_->ParseCommandLine(argc, argv);
        
        // 处理帮助和版本信息
        if (args.help) {
            cli_handler_->ShowHelp();
            return ExitCode::SUCCESS;
        }
        
        if (args.version) {
            cli_handler_->ShowVersion();
            return ExitCode::SUCCESS;
        }

        if (args.diagnose) {
            return RunDiagnosticMode(args);
        }
        
        // 初始化应用程序
        if (!Initialize(args.config_path)) {
            return ExitCode::CONFIG_ERROR;
        }

        // 应用命令行参数覆盖配置
        ApplyCommandLineOverrides(args);
        
        SetState(AppState::READY);
        
        // 根据参数决定运行模式
        if (args.interactive || args.uid.empty()) {
            return RunInteractiveMode();
        } else {
            return RunCommandLineMode(args);
        }
        
    } catch (const std::exception& e) {
        return HandleException(e);
    }
}

bool MainController::Initialize(const std::string& config_path) {
    try {
        // 加载配置
        if (!config_manager_->LoadConfig(config_path)) {
            SetError("配置文件加载失败");
            return false;
        }
        
        // 初始化日志系统
        if (!InitializeLogging()) {
            SetError("日志系统初始化失败");
            return false;
        }
        
        LOG_INFO("PixivTagDownloader 启动中...");

        // 创建认证管理器
        LOG_DEBUG("创建认证管理器...");
        pixiv_auth_ = std::make_unique<auth::PixivAuth>(config_manager_->GetConfig().auth);
        LOG_DEBUG("认证管理器创建成功");

        // 验证认证
        LOG_DEBUG("开始验证认证...");
        if (!ValidateAuthentication()) {
            SetError("Pixiv认证失败");
            return false;
        }
        LOG_DEBUG("认证验证成功");

        // 创建API客户端
        LOG_DEBUG("创建API客户端...");
        api_client_ = std::make_unique<api::PixivApiClient>(
            config_manager_->GetConfig(), *pixiv_auth_);
        LOG_DEBUG("API客户端对象创建成功");

        LOG_DEBUG("初始化API客户端...");
        if (!api_client_->Initialize()) {
            SetError("API客户端初始化失败");
            return false;
        }
        LOG_DEBUG("API客户端初始化成功");

        // 重新创建CLI处理器以传入API客户端
        cli_handler_ = std::make_unique<cli::CliHandler>(*config_manager_, api_client_.get());
        
        // 创建存储管理器
        storage_manager_ = std::make_unique<storage::StorageManager>(
            config_manager_->GetConfig());
        
        if (!storage_manager_->Initialize()) {
            SetError("存储管理器初始化失败");
            return false;
        }
        
        // 创建下载器
        downloader_ = std::make_unique<download::DownloaderCore>(
            config_manager_->GetConfig(), *api_client_, *storage_manager_);
        
        if (!downloader_->Initialize()) {
            SetError("下载器初始化失败");
            return false;
        }
        
        initialized_ = true;
        LOG_INFO("应用程序初始化完成");
        return true;
        
    } catch (const std::exception& e) {
        SetError("初始化过程中发生异常: " + std::string(e.what()));
        return false;
    }
}

void MainController::ApplyCommandLineOverrides(const cli::CommandLineArgs& args) {
    auto& config = config_manager_->GetMutableConfig();

    // 覆盖输出目录
    if (!args.output_dir.empty()) {
        config.paths.output_root_dir = args.output_dir;
        LOG_INFO("命令行参数覆盖输出目录: {}", args.output_dir);
    }

    // 覆盖下载方式
    if (!args.download_method.empty()) {
        config.download_method = config::ConfigManager::StringToDownloadMethod(args.download_method);
        LOG_INFO("命令行参数覆盖下载方式: {}", args.download_method);
    }

    // 覆盖并发数
    if (args.concurrency > 0) {
        config.concurrency = args.concurrency;
        LOG_INFO("命令行参数覆盖并发数: {}", args.concurrency);
    }

    // 覆盖延迟设置
    if (!args.delay_range.empty()) {
        auto parts = utilities::StringUtils::Split(args.delay_range, "-");
        if (parts.size() == 2) {
            try {
                config.delay.min_delay = std::stoi(parts[0]);
                config.delay.max_delay = std::stoi(parts[1]);
                LOG_INFO("命令行参数覆盖延迟范围: {}-{}", config.delay.min_delay, config.delay.max_delay);
            } catch (const std::exception& e) {
                LOG_WARN("无效的延迟范围格式: {}", args.delay_range);
            }
        }
    }

    // 覆盖文件冲突策略
    if (!args.file_conflict.empty()) {
        config.file_conflict = config::ConfigManager::StringToFileConflictStrategy(args.file_conflict);
        LOG_INFO("命令行参数覆盖文件冲突策略: {}", args.file_conflict);
    }

    // 覆盖日志级别
    if (!args.log_level.empty()) {
        config.logging.level = utilities::Logger::StringToLevel(args.log_level);
        LOG_INFO("命令行参数覆盖日志级别: {}", args.log_level);
    }

    // 覆盖日志路径
    if (!args.log_path.empty()) {
        config.logging.log_path = args.log_path;
        LOG_INFO("命令行参数覆盖日志路径: {}", args.log_path);
    }
}

void MainController::Shutdown() {
    if (initialized_) {
        LOG_INFO("正在关闭应用程序...");
        
        SetState(AppState::STOPPING);
        
        // 停止下载器
        if (downloader_) {
            downloader_->Stop(true);
        }
        
        // 清理API客户端
        if (api_client_) {
            api_client_->Cleanup();
        }
        
        // 清理资源
        CleanupResources();
        
        SetState(AppState::STOPPED);
        LOG_INFO("应用程序已关闭");
        
        // 关闭日志系统
        utilities::Logger::Shutdown();
        
        initialized_ = false;
    }
}

ExitCode MainController::RunDiagnosticMode(const cli::CommandLineArgs& args) {
    std::cout << "\n=== PixivTagDownloader 配置诊断 ===" << std::endl;

    try {
        // 初始化应用程序（仅加载配置）
        if (!Initialize(args.config_path)) {
            std::cout << "❌ 配置初始化失败" << std::endl;
            return ExitCode::CONFIG_ERROR;
        }

        const auto& config = config_manager_->GetConfig();

        std::cout << "✅ 配置文件加载成功: " << args.config_path << std::endl;
        std::cout << "\n📁 路径配置检查:" << std::endl;
        std::cout << "  输出根目录: '" << config.paths.output_root_dir << "'" << std::endl;
        std::cout << "  作品路径模板: '" << config.paths.artwork_path_template << "'" << std::endl;
        std::cout << "  单图命名模板: '" << config.paths.single_image_naming_template << "'" << std::endl;

        // 检查路径配置的合理性
        if (config.paths.output_root_dir == "unnamed" || config.paths.output_root_dir == "unknown_user") {
            std::cout << "⚠️  警告：输出根目录配置异常，这可能导致路径生成错误" << std::endl;
        }

        // 模拟路径生成测试
        std::cout << "\n🧪 路径生成测试:" << std::endl;

        // 创建测试用的作品和用户信息
        api::ArtworkInfo test_artwork;
        test_artwork.pid = "123456789";
        test_artwork.title = "测试作品";
        test_artwork.type = config::ArtworkType::ILLUST;
        test_artwork.page_count = 1;

        api::UserInfo test_user;
        test_user.uid = "87654321";
        test_user.username = "测试用户";

        // 测试正常情况
        auto path_result = storage_manager_->GenerateSingleImagePath(test_artwork, test_user, ".jpg");
        std::cout << "  正常用户路径: " << path_result.full_path << std::endl;

        // 测试用户名为空的情况
        test_user.username = "";
        path_result = storage_manager_->GenerateSingleImagePath(test_artwork, test_user, ".jpg");
        std::cout << "  空用户名路径: " << path_result.full_path << std::endl;

        std::cout << "\n🔧 其他配置:" << std::endl;
        std::cout << "  并发数: " << config.concurrency << std::endl;
        std::cout << "  下载方式: " << config::ConfigManager::DownloadMethodToString(config.download_method) << std::endl;
        std::cout << "  文件冲突策略: " << config::ConfigManager::FileConflictStrategyToString(config.file_conflict) << std::endl;

        std::cout << "\n✅ 诊断完成" << std::endl;
        std::cout << "\n💡 如果路径生成异常，请检查：" << std::endl;
        std::cout << "   1. 配置文件是否正确加载" << std::endl;
        std::cout << "   2. output_root_dir 是否设置正确" << std::endl;
        std::cout << "   3. 用户信息是否能正常获取" << std::endl;

        return ExitCode::SUCCESS;

    } catch (const std::exception& e) {
        std::cout << "❌ 诊断过程中发生错误: " << e.what() << std::endl;
        return ExitCode::RUNTIME_ERROR;
    }
}

ExitCode MainController::RunInteractiveMode() {
    try {
        cli_handler_->ShowWelcome();
        
        bool continue_running = true;
        while (continue_running) {
            // 获取用户选择
            auto selection = cli_handler_->RunInteractiveMode();
            
            // 确认选择
            if (!cli_handler_->ConfirmSelection(selection)) {
                continue;
            }
            
            // 执行下载任务
            auto result = ExecuteDownloadTask(selection);

            // 显示执行摘要
            ShowExecutionSummary(result);

            // 处理失败任务重试
            auto user_info = GetUserInfo(selection.uid);
            HandleFailedTasksRetry(result, selection, user_info);

            // 询问是否继续
            continue_running = cli_handler_->AskForNewTask();
        }
        
        return ExitCode::SUCCESS;
        
    } catch (const std::exception& e) {
        return HandleException(e);
    }
}

ExitCode MainController::RunCommandLineMode(const cli::CommandLineArgs& args) {
    try {
        // 从命令行参数创建用户选择
        auto selection = cli_handler_->CreateSelectionFromArgs(args);
        
        // 执行下载任务
        auto result = ExecuteDownloadTask(selection);

        // 显示执行摘要
        ShowExecutionSummary(result);

        // 处理失败任务重试
        auto user_info = GetUserInfo(selection.uid);
        HandleFailedTasksRetry(result, selection, user_info);

        return result.success ? ExitCode::SUCCESS : ExitCode::RUNTIME_ERROR;
        
    } catch (const std::exception& e) {
        return HandleException(e);
    }
}

TaskExecutionResult MainController::ExecuteDownloadTask(const cli::UserSelection& selection) {
    TaskExecutionResult result;
    result.start_time = std::chrono::steady_clock::now();
    
    try {
        SetState(AppState::RUNNING);
        
        LOG_INFO("开始执行下载任务，用户ID: {}", selection.uid);
        
        // 获取用户信息
        auto user_info = GetUserInfo(selection.uid);
        if (user_info.uid.empty()) {
            result.error_message = "无法获取用户信息";
            return result;
        }
        
        // 获取用户作品列表
        auto artworks = GetUserArtworks(selection.uid, selection);
        if (artworks.empty()) {
            result.error_message = "未找到符合条件的作品";
            return result;
        }
        
        LOG_INFO("找到 {} 个符合条件的作品", artworks.size());
        result.total_artworks = static_cast<int>(artworks.size());
        
        // 创建下载任务
        auto download_tasks = CreateDownloadTasks(artworks, user_info);
        
        // 设置进度回调
        cli_handler_->SetupProgressCallback(*downloader_);
        
        // 启动下载器
        int worker_count = config_manager_->GetConfig().concurrency;
        if (!downloader_->Start(worker_count)) {
            result.error_message = "下载器启动失败";
            return result;
        }
        
        // 添加下载任务
        downloader_->AddTasks(download_tasks);
        
        // 等待完成
        downloader_->WaitForCompletion();
        
        // 获取结果
        auto download_results = downloader_->GetAllResults();
        
        // 统计结果
        for (const auto& download_result : download_results) {
            switch (download_result.status) {
                case download::DownloadStatus::COMPLETED:
                    result.downloaded_artworks++;
                    result.total_bytes += download_result.file_size;
                    break;
                case download::DownloadStatus::FAILED:
                    result.failed_artworks++;
                    result.failed_tasks.push_back(download_result); // 收集失败的任务
                    break;
                case download::DownloadStatus::SKIPPED:
                    result.skipped_artworks++;
                    break;
                default:
                    break;
            }
        }
        
        result.success = (result.failed_artworks == 0);
        
    } catch (const std::exception& e) {
        result.error_message = e.what();
        LOG_ERROR("执行下载任务时发生异常: {}", e.what());
    }
    
    result.end_time = std::chrono::steady_clock::now();
    SetState(AppState::READY);
    
    return result;
}

bool MainController::InitializeLogging() {
    const auto& log_config = config_manager_->GetConfig().logging;
    return utilities::Logger::Initialize(
        log_config.level,
        log_config.log_path,
        log_config.max_file_size,
        log_config.max_files
    );
}

bool MainController::ValidateAuthentication() {
    const auto& config = config_manager_->GetConfig();

    LOG_DEBUG("检查Cookie配置...");
    if (config.pixiv_cookie.empty()) {
        LOG_ERROR("Pixiv Cookie 未配置");
        return false;
    }
    LOG_DEBUG("Cookie配置存在，长度: {}", config.pixiv_cookie.length());

    LOG_DEBUG("设置Cookie字符串...");
    if (!pixiv_auth_->SetCookieString(config.pixiv_cookie)) {
        LOG_ERROR("Cookie 设置失败");
        return false;
    }
    LOG_DEBUG("Cookie字符串设置成功");

    LOG_DEBUG("开始Cookie验证...");
    auto auth_status = pixiv_auth_->ValidateCookie();
    if (auth_status != auth::AuthStatus::AUTHENTICATED) {
        LOG_ERROR("Cookie 验证失败: {}", auth::PixivAuth::AuthStatusToString(auth_status));
        return false;
    }

    LOG_INFO("Pixiv 认证成功");
    return true;
}

api::UserInfo MainController::GetUserInfo(const std::string& uid) {
    return api_client_->GetUserInfo(uid);
}

std::vector<api::ArtworkInfo> MainController::GetUserArtworks(const std::string& uid,
                                                             const cli::UserSelection& selection) {
    // 获取用户所有作品ID
    auto artwork_ids = api_client_->GetUserArtworkIds(uid);

    std::vector<api::ArtworkInfo> artworks;
    int total_count = 0;
    int processed_count = 0;

    // 计算总数
    if (artwork_ids.find("illustrations") != artwork_ids.end()) {
        total_count += artwork_ids["illustrations"].size();
    }
    if (artwork_ids.find("novels") != artwork_ids.end()) {
        total_count += artwork_ids["novels"].size();
    }

    if (total_count == 0) {
        LOG_WARN("用户 {} 没有公开作品", uid);
        return artworks;
    }

    LOG_INFO("开始获取 {} 个作品的详细信息...", total_count);
    std::cout << "📥 正在获取作品详细信息，这可能需要一些时间..." << std::endl;

    // 处理插画和漫画
    if (artwork_ids.find("illustrations") != artwork_ids.end()) {
        for (const auto& pid : artwork_ids["illustrations"]) {
            auto artwork = api_client_->GetArtworkDetails(pid);
            if (!artwork.pid.empty()) {
                artworks.push_back(artwork);
            }
            processed_count++;

            // 显示进度
            if (processed_count % 10 == 0 || processed_count == total_count) {
                double progress = (double)processed_count / total_count * 100.0;
                std::cout << "📊 进度: " << processed_count << "/" << total_count
                         << " (" << std::fixed << std::setprecision(1) << progress << "%)" << std::endl;
            }
        }
    }

    // 处理小说
    if (artwork_ids.find("novels") != artwork_ids.end()) {
        for (const auto& pid : artwork_ids["novels"]) {
            auto artwork = api_client_->GetNovelDetails(pid);
            if (!artwork.pid.empty()) {
                artworks.push_back(artwork);
            }
            processed_count++;

            // 显示进度
            if (processed_count % 10 == 0 || processed_count == total_count) {
                double progress = (double)processed_count / total_count * 100.0;
                std::cout << "📊 进度: " << processed_count << "/" << total_count
                         << " (" << std::fixed << std::setprecision(1) << progress << "%)" << std::endl;
            }
        }
    }

    std::cout << "✅ 作品信息获取完成，共获取 " << artworks.size() << " 个作品" << std::endl;
    LOG_INFO("作品信息获取完成，共 {} 个作品", artworks.size());

    // 应用过滤器
    auto filtered_artworks = FilterArtworks(artworks, selection);
    if (filtered_artworks.size() != artworks.size()) {
        std::cout << "🔍 应用过滤条件后，剩余 " << filtered_artworks.size() << " 个作品" << std::endl;
        LOG_INFO("过滤后剩余 {} 个作品", filtered_artworks.size());
    }

    return filtered_artworks;
}

std::vector<api::ArtworkInfo> MainController::FilterArtworks(const std::vector<api::ArtworkInfo>& artworks,
                                                            const cli::UserSelection& selection) {
    std::vector<api::ArtworkInfo> filtered_artworks;
    
    for (const auto& artwork : artworks) {
        // 应用类型过滤
        if (!ApplyTypeFilter(artwork, selection.artwork_types)) {
            continue;
        }
        
        // 如果不是下载所有作品，应用标签过滤
        if (!selection.download_all_works) {
            if (!ApplyTagFilter(artwork, selection.tags, selection.tag_logic)) {
                continue;
            }
        }
        
        filtered_artworks.push_back(artwork);
    }
    
    return filtered_artworks;
}

bool MainController::ApplyTypeFilter(const api::ArtworkInfo& artwork,
                                   const std::vector<config::ArtworkType>& types) {
    // 如果包含ALL类型，接受所有作品
    for (const auto& type : types) {
        if (type == config::ArtworkType::ALL || type == artwork.type) {
            return true;
        }
    }
    return false;
}

bool MainController::ApplyTagFilter(const api::ArtworkInfo& artwork,
                                  const std::vector<std::string>& tags,
                                  config::TagFilterLogic logic) {
    if (tags.empty()) {
        return true; // 没有标签过滤条件
    }
    
    switch (logic) {
        case config::TagFilterLogic::AND: {
            // 所有标签都必须匹配
            for (const auto& filter_tag : tags) {
                bool found = false;
                for (const auto& artwork_tag : artwork.tags) {
                    if (artwork_tag == filter_tag) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    return false;
                }
            }
            return true;
        }
        
        case config::TagFilterLogic::OR: {
            // 任意标签匹配
            for (const auto& filter_tag : tags) {
                for (const auto& artwork_tag : artwork.tags) {
                    if (artwork_tag == filter_tag) {
                        return true;
                    }
                }
            }
            return false;
        }
        
        case config::TagFilterLogic::NOT: {
            // 排除包含指定标签的作品
            for (const auto& filter_tag : tags) {
                for (const auto& artwork_tag : artwork.tags) {
                    if (artwork_tag == filter_tag) {
                        return false;
                    }
                }
            }
            return true;
        }
        
        default:
            return true;
    }
}

std::vector<download::DownloadTask> MainController::CreateDownloadTasks(
    const std::vector<api::ArtworkInfo>& artworks,
    const api::UserInfo& user_info) {
    
    std::vector<download::DownloadTask> tasks;
    
    for (const auto& artwork : artworks) {
        auto artwork_tasks = CreateTasksForArtwork(artwork, user_info);
        tasks.insert(tasks.end(), artwork_tasks.begin(), artwork_tasks.end());
    }
    
    return tasks;
}

std::vector<download::DownloadTask> MainController::CreateTasksForArtwork(
    const api::ArtworkInfo& artwork,
    const api::UserInfo& user_info) {
    
    std::vector<download::DownloadTask> tasks;
    
    if (artwork.type == config::ArtworkType::NOVEL) {
        // 小说任务
        auto path_result = storage_manager_->GenerateNovelPath(artwork, user_info);
        
        download::DownloadTask task;
        task.url = ""; // 小说不需要下载URL，内容已在artwork.content中
        task.output_path = path_result.full_path;
        task.artwork_info = artwork;
        task.task_id = "novel_" + artwork.pid;
        
        tasks.push_back(task);
        
    } else {
        // 图片任务
        if (artwork.page_count == 1) {
            // 单页图片
            if (!artwork.image_urls.empty()) {
                auto path_result = storage_manager_->GenerateSingleImagePath(
                    artwork, user_info, utilities::FileUtils::GetFileExtension(artwork.image_urls[0]));
                
                download::DownloadTask task;
                task.url = artwork.image_urls[0];
                task.output_path = path_result.full_path;
                task.artwork_info = artwork;
                task.page_index = 0;
                task.task_id = "image_" + artwork.pid + "_p0";
                
                tasks.push_back(task);
            }
        } else {
            // 多页图片
            for (int i = 0; i < artwork.page_count && i < static_cast<int>(artwork.image_urls.size()); ++i) {
                auto path_result = storage_manager_->GenerateMultiImagePath(
                    artwork, user_info, i, utilities::FileUtils::GetFileExtension(artwork.image_urls[i]));
                
                download::DownloadTask task;
                task.url = artwork.image_urls[i];
                task.output_path = path_result.full_path;
                task.artwork_info = artwork;
                task.page_index = i;
                task.task_id = "image_" + artwork.pid + "_p" + std::to_string(i);
                
                tasks.push_back(task);
            }
        }
    }
    
    return tasks;
}

void MainController::ShowExecutionSummary(const TaskExecutionResult& result) {
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        result.end_time - result.start_time).count();

    std::cout << "\n=== 下载完成摘要 ===" << std::endl;
    std::cout << "总作品数: " << result.total_artworks << std::endl;
    std::cout << "成功下载: " << result.downloaded_artworks << std::endl;
    std::cout << "下载失败: " << result.failed_artworks << std::endl;
    std::cout << "跳过文件: " << result.skipped_artworks << std::endl;
    std::cout << "总用时: " << duration << " 秒" << std::endl;

    if (!result.error_message.empty()) {
        std::cout << "错误信息: " << result.error_message << std::endl;
    }

    std::cout << "===================" << std::endl;
}

bool MainController::HandleFailedTasksRetry(TaskExecutionResult& result,
                                           const cli::UserSelection& selection,
                                           const api::UserInfo& user_info) {
    // 如果没有失败的任务，直接返回
    if (result.failed_tasks.empty()) {
        return false;
    }

    std::cout << "\n⚠️  检测到 " << result.failed_tasks.size() << " 个下载失败的任务" << std::endl;

    // 显示失败任务的详细信息
    std::cout << "\n失败任务详情：" << std::endl;
    for (size_t i = 0; i < result.failed_tasks.size() && i < 10; ++i) {
        const auto& failed_task = result.failed_tasks[i];
        std::cout << "  " << (i + 1) << ". " << failed_task.file_path << std::endl;
        if (!failed_task.error_message.empty()) {
            std::cout << "     错误: " << failed_task.error_message << std::endl;
        }
    }

    if (result.failed_tasks.size() > 10) {
        std::cout << "  ... 还有 " << (result.failed_tasks.size() - 10) << " 个失败任务" << std::endl;
    }

    // 询问用户是否重试
    bool retry = cli_handler_->GetUserConfirmation("\n是否重试失败的下载任务？", true);
    if (!retry) {
        return false;
    }

    std::cout << "\n🔄 开始重试失败的任务..." << std::endl;

    // 重新创建失败的下载任务
    std::vector<download::DownloadTask> retry_tasks;

    // 重新获取用户作品信息以重建任务
    auto artworks = GetUserArtworks(selection.uid, selection);
    auto download_tasks = CreateDownloadTasks(artworks, user_info);

    // 只保留失败的任务进行重试
    for (const auto& failed_result : result.failed_tasks) {
        for (const auto& task : download_tasks) {
            if (task.output_path == failed_result.file_path) {
                download::DownloadTask retry_task = task;
                retry_task.task_id = task.task_id + "_retry";
                retry_task.retry_count = 1; // 标记为重试任务
                retry_tasks.push_back(retry_task);
                break;
            }
        }
    }

    if (retry_tasks.empty()) {
        std::cout << "❌ 无法重建重试任务" << std::endl;
        return false;
    }

    // 重置下载器状态
    downloader_->Stop();
    downloader_->ClearCompletedResults();

    // 启动下载器进行重试
    int worker_count = config_manager_->GetConfig().concurrency;
    if (!downloader_->Start(worker_count)) {
        std::cout << "❌ 重试下载器启动失败" << std::endl;
        return false;
    }

    // 添加重试任务
    downloader_->AddTasks(retry_tasks);

    // 等待重试完成
    downloader_->WaitForCompletion();

    // 获取重试结果
    auto retry_results = downloader_->GetAllResults();

    // 统计重试结果
    int retry_success = 0;
    int retry_failed = 0;

    for (const auto& retry_result : retry_results) {
        if (retry_result.status == download::DownloadStatus::COMPLETED) {
            retry_success++;
        } else if (retry_result.status == download::DownloadStatus::FAILED) {
            retry_failed++;
        }
    }

    // 更新总体结果
    result.downloaded_artworks += retry_success;
    result.failed_artworks = retry_failed;

    // 显示重试结果
    std::cout << "\n=== 重试结果 ===" << std::endl;
    std::cout << "重试成功: " << retry_success << std::endl;
    std::cout << "仍然失败: " << retry_failed << std::endl;
    std::cout << "===============" << std::endl;

    // 更新成功状态
    result.success = (result.failed_artworks == 0);

    return true;
}

ExitCode MainController::HandleException(const std::exception& e) {
    SetError(e.what());
    LOG_FATAL("程序异常: {}", e.what());
    cli_handler_->ShowError("程序发生异常: " + std::string(e.what()));
    return ExitCode::RUNTIME_ERROR;
}

void MainController::CleanupResources() {
    // 清理各种资源
    downloader_.reset();
    storage_manager_.reset();
    api_client_.reset();
    pixiv_auth_.reset();
    cli_handler_.reset();
    config_manager_.reset();
}

void MainController::SetState(AppState state) {
    app_state_ = state;
    LOG_DEBUG("应用程序状态更新为: {}", AppStateToString(state));
}

void MainController::SetError(const std::string& error) {
    last_error_ = error;
    LOG_ERROR(error);
}

std::string MainController::ExitCodeToString(ExitCode code) {
    switch (code) {
        case ExitCode::SUCCESS: return "成功";
        case ExitCode::CONFIG_ERROR: return "配置错误";
        case ExitCode::AUTH_ERROR: return "认证错误";
        case ExitCode::NETWORK_ERROR: return "网络错误";
        case ExitCode::STORAGE_ERROR: return "存储错误";
        case ExitCode::USER_CANCELLED: return "用户取消";
        case ExitCode::INVALID_ARGUMENTS: return "无效参数";
        case ExitCode::RUNTIME_ERROR: return "运行时错误";
        default: return "未知错误";
    }
}

std::string MainController::AppStateToString(AppState state) {
    switch (state) {
        case AppState::INITIALIZING: return "初始化中";
        case AppState::READY: return "就绪";
        case AppState::RUNNING: return "运行中";
        case AppState::PAUSED: return "暂停";
        case AppState::STOPPING: return "停止中";
        case AppState::STOPPED: return "已停止";
        case AppState::ERROR: return "错误";
        default: return "未知状态";
    }
}

} // namespace core
} // namespace pixiv_downloader
