#include "download/downloader_core.h"
#include "download/aria2c_downloader.h"
#include "utilities/logger.h"
#include "utilities/file_utils.h"
#include <algorithm>
#include <chrono>

namespace pixiv_downloader {
namespace download {

DownloaderCore::DownloaderCore(const config::Config& config, 
                               api::PixivApiClient& api_client,
                               storage::StorageManager& storage_manager)
    : config_(config), api_client_(api_client), storage_manager_(storage_manager),
      is_running_(false), should_stop_(false), is_paused_(false), active_workers_(0),
      task_counter_(0), total_tasks_(0), completed_tasks_(0), failed_tasks_(0), 
      skipped_tasks_(0), total_bytes_(0), downloaded_bytes_(0) {
}

DownloaderCore::~DownloaderCore() {
    Stop(false);
}

bool DownloaderCore::Initialize() {
    // 初始化Aria2c下载器
    if (config_.download_method == config::DownloadMethod::ARIA2C) {
        aria2c_downloader_ = std::make_unique<Aria2cDownloader>(
            config_.aria2c_path, config_.aria2c_options);

        if (!aria2c_downloader_->IsAvailable()) {
            LOG_WARN("Aria2c不可用，将回退到直接下载");
        } else {
            LOG_INFO("Aria2c下载器初始化成功，版本: {}", aria2c_downloader_->GetVersion());
        }
    }

    LOG_INFO("下载器核心初始化成功");
    return true;
}

bool DownloaderCore::Start(int worker_count) {
    if (is_running_) {
        LOG_WARN("下载器已在运行");
        return false;
    }
    
    if (worker_count <= 0) {
        worker_count = config_.concurrency;
    }
    
    should_stop_ = false;
    is_paused_ = false;
    is_running_ = true;
    
    // 启动工作线程
    for (int i = 0; i < worker_count; ++i) {
        worker_threads_.emplace_back(&DownloaderCore::WorkerThread, this, i);
    }
    
    LOG_INFO("下载器启动成功，工作线程数: {}", worker_count);
    return true;
}

void DownloaderCore::Stop(bool wait_for_completion) {
    if (!is_running_) {
        return;
    }
    
    LOG_INFO("正在停止下载器...");
    should_stop_ = true;
    
    // 唤醒所有等待的线程
    queue_condition_.notify_all();
    
    if (wait_for_completion) {
        // 等待所有工作线程完成
        for (auto& thread : worker_threads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
    } else {
        // 分离线程，让它们自然结束
        for (auto& thread : worker_threads_) {
            if (thread.joinable()) {
                thread.detach();
            }
        }
    }
    
    worker_threads_.clear();
    is_running_ = false;
    
    LOG_INFO("下载器已停止");
}

std::string DownloaderCore::AddTask(const DownloadTask& task) {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    
    DownloadTask new_task = task;
    if (new_task.task_id.empty()) {
        new_task.task_id = GenerateTaskId();
    }
    
    task_queue_.push(new_task);
    total_tasks_++;
    
    // 唤醒一个工作线程
    queue_condition_.notify_one();
    
    LOG_DEBUG("添加下载任务: {}", new_task.task_id);
    return new_task.task_id;
}

std::vector<std::string> DownloaderCore::AddTasks(const std::vector<DownloadTask>& tasks) {
    std::vector<std::string> task_ids;
    
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        
        for (const auto& task : tasks) {
            DownloadTask new_task = task;
            if (new_task.task_id.empty()) {
                new_task.task_id = GenerateTaskId();
            }
            
            task_queue_.push(new_task);
            task_ids.push_back(new_task.task_id);
        }
        
        total_tasks_ += tasks.size();
    }
    
    // 唤醒所有工作线程
    queue_condition_.notify_all();
    
    LOG_INFO("添加 {} 个下载任务", tasks.size());
    return task_ids;
}

bool DownloaderCore::WaitForCompletion(int timeout_ms) {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    
    if (timeout_ms <= 0) {
        // 无限等待
        completion_condition_.wait(lock, [this] {
            return task_queue_.empty() && active_workers_ == 0;
        });
        return true;
    } else {
        // 超时等待
        return completion_condition_.wait_for(lock, std::chrono::milliseconds(timeout_ms), [this] {
            return task_queue_.empty() && active_workers_ == 0;
        });
    }
}

DownloadProgress DownloaderCore::GetProgress() const {
    DownloadProgress progress;
    
    progress.total_tasks = total_tasks_;
    progress.completed_tasks = completed_tasks_;
    progress.failed_tasks = failed_tasks_;
    progress.skipped_tasks = skipped_tasks_;
    progress.total_bytes = total_bytes_;
    progress.downloaded_bytes = downloaded_bytes_;
    
    if (progress.total_tasks > 0) {
        progress.overall_progress = (static_cast<double>(progress.completed_tasks + progress.failed_tasks + progress.skipped_tasks) 
                                   / progress.total_tasks) * 100.0;
    }
    
    return progress;
}

std::vector<DownloadResult> DownloaderCore::GetAllResults() const {
    std::lock_guard<std::mutex> lock(results_mutex_);
    
    std::vector<DownloadResult> results;
    for (const auto& [task_id, result] : task_results_) {
        results.push_back(result);
    }
    
    return results;
}

void DownloaderCore::SetProgressCallback(ProgressCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    progress_callback_ = callback;
}

void DownloaderCore::SetTaskCompletionCallback(TaskCompletionCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    task_completion_callback_ = callback;
}

size_t DownloaderCore::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(queue_mutex_);
    return task_queue_.size();
}

void DownloaderCore::Pause() {
    is_paused_ = true;
    LOG_INFO("下载器已暂停");
}

void DownloaderCore::Resume() {
    is_paused_ = false;
    queue_condition_.notify_all();
    LOG_INFO("下载器已恢复");
}

void DownloaderCore::ClearCompletedResults() {
    std::lock_guard<std::mutex> lock(results_mutex_);
    task_results_.clear();

    // 重置统计计数器
    completed_tasks_ = 0;
    failed_tasks_ = 0;
    skipped_tasks_ = 0;
    total_bytes_ = 0;
    downloaded_bytes_ = 0;

    LOG_DEBUG("已清除所有任务结果");
}

// 私有方法实现
void DownloaderCore::WorkerThread(int worker_id) {
    LOG_DEBUG("工作线程 {} 启动", worker_id);
    
    while (!should_stop_) {
        DownloadTask task;
        
        // 获取任务
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            
            // 等待任务或停止信号
            queue_condition_.wait(lock, [this] {
                return !task_queue_.empty() || should_stop_;
            });
            
            if (should_stop_) {
                break;
            }
            
            if (task_queue_.empty()) {
                continue;
            }
            
            task = task_queue_.front();
            task_queue_.pop();
            active_workers_++;
        }
        
        // 检查暂停状态
        while (is_paused_ && !should_stop_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (should_stop_) {
            break;
        }
        
        // 处理任务
        auto result = ProcessTask(task);
        
        // 保存结果
        {
            std::lock_guard<std::mutex> lock(results_mutex_);
            task_results_[task.task_id] = result;
        }
        
        // 更新统计
        switch (result.status) {
            case DownloadStatus::COMPLETED:
                completed_tasks_++;
                downloaded_bytes_ += result.file_size;
                break;
            case DownloadStatus::FAILED:
                failed_tasks_++;
                break;
            case DownloadStatus::SKIPPED:
                skipped_tasks_++;
                break;
            default:
                break;
        }
        
        // 通知任务完成
        NotifyTaskCompletion(result);
        
        // 更新进度
        UpdateProgress();
        
        // 减少活跃工作线程计数
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            active_workers_--;
            
            // 如果没有更多任务且没有活跃工作线程，通知完成
            if (task_queue_.empty() && active_workers_ == 0) {
                completion_condition_.notify_all();
            }
        }
    }
    
    LOG_DEBUG("工作线程 {} 结束", worker_id);
}

DownloadResult DownloaderCore::ProcessTask(const DownloadTask& task) {
    DownloadResult result;
    result.task_id = task.task_id;
    result.start_time = std::chrono::steady_clock::now();
    result.status = DownloadStatus::PENDING;
    
    try {
        LOG_DEBUG("开始处理任务: {}", task.task_id);
        
        // 检查文件冲突
        std::string final_path = HandleFileConflict(task.output_path);
        if (final_path.empty()) {
            // 跳过文件
            result.status = DownloadStatus::SKIPPED;
            result.file_path = task.output_path;
            LOG_INFO("跳过已存在的文件: {}", task.output_path);
            return result;
        }
        
        result.file_path = final_path;
        
        // 创建输出目录
        std::string dir = utilities::FileUtils::GetDirectoryPath(final_path);
        if (!storage_manager_.CreateDirectory(dir)) {
            result.status = DownloadStatus::FAILED;
            result.error_message = "无法创建输出目录: " + dir;
            return result;
        }
        
        // 根据下载方式执行下载
        if (task.artwork_info.type == config::ArtworkType::NOVEL) {
            // 小说直接保存内容
            api::UserInfo user_info;
            user_info.uid = task.artwork_info.author_uid;
            user_info.username = task.artwork_info.author_username;
            user_info.profile_image_url = "";
            user_info.background_image_url = "";
            user_info.comment = "";

            if (storage_manager_.CreateNovelMetadata(task.artwork_info, user_info, final_path)) {
                result.status = DownloadStatus::COMPLETED;
                result.file_size = utilities::FileUtils::GetFileSize(final_path);
            } else {
                result.status = DownloadStatus::FAILED;
                result.error_message = "小说文件创建失败";
            }
        } else {
            // 图片下载
            switch (config_.download_method) {
                case config::DownloadMethod::DIRECT:
                    result = PerformDirectDownload(task);
                    break;
                case config::DownloadMethod::ARIA2C:
                    result = PerformAria2cDownload(task);
                    break;
                default:
                    result = PerformDirectDownload(task);
                    break;
            }
        }
        
        // 创建元数据文件
        if (result.status == DownloadStatus::COMPLETED) {
            api::UserInfo user_info;
            user_info.uid = task.artwork_info.author_uid;
            user_info.username = task.artwork_info.author_username;
            user_info.profile_image_url = "";
            user_info.background_image_url = "";
            user_info.comment = "";

            auto metadata_path = storage_manager_.GenerateMetadataPath(
                task.artwork_info, user_info, final_path);
            
            if (task.artwork_info.type == config::ArtworkType::NOVEL) {
                // 小说的元数据已经包含在文件中
            } else {
                storage_manager_.CreateArtworkMetadata(
                    task.artwork_info, user_info, metadata_path.full_path);
            }
        }
        
    } catch (const std::exception& e) {
        result.status = DownloadStatus::FAILED;
        result.error_message = e.what();
        LOG_ERROR("处理任务时发生异常: {}", e.what());
    }
    
    result.end_time = std::chrono::steady_clock::now();
    return result;
}

DownloadResult DownloaderCore::PerformDirectDownload(const DownloadTask& task) {
    DownloadResult result;
    result.task_id = task.task_id;
    result.file_path = task.output_path;
    result.status = DownloadStatus::DOWNLOADING;
    
    // 使用API客户端下载文件
    bool success = api_client_.DownloadFile(task.url, task.output_path,
        [&](size_t /*downloaded*/, size_t /*total*/) -> bool {
            // 进度回调
            return !should_stop_;
        });
    
    if (success) {
        result.status = DownloadStatus::COMPLETED;
        result.file_size = utilities::FileUtils::GetFileSize(task.output_path);
        LOG_DEBUG("直接下载完成: {}", task.output_path);
    } else {
        result.status = DownloadStatus::FAILED;
        result.error_message = api_client_.GetLastError();
        LOG_ERROR("直接下载失败: {}", result.error_message);
    }
    
    return result;
}

DownloadResult DownloaderCore::PerformAria2cDownload(const DownloadTask& task) {
    DownloadResult result;
    result.task_id = task.task_id;
    result.file_path = task.output_path;
    result.status = DownloadStatus::DOWNLOADING;

    // 检查Aria2c是否可用
    if (!aria2c_downloader_ || !aria2c_downloader_->IsAvailable()) {
        LOG_WARN("Aria2c不可用，回退到直接下载");
        return PerformDirectDownload(task);
    }

    try {
        // 创建Aria2c任务
        Aria2cTask aria2c_task;
        aria2c_task.url = task.url;
        aria2c_task.output_path = task.output_path;
        aria2c_task.referer = config_.http.referer;
        aria2c_task.user_agent = config_.http.user_agent;
        aria2c_task.cookie = api_client_.GetCookieString();

        // 执行下载
        auto aria2c_result = aria2c_downloader_->DownloadFile(aria2c_task);

        if (aria2c_result.success) {
            result.status = DownloadStatus::COMPLETED;
            result.file_size = aria2c_result.file_size;
            LOG_DEBUG("Aria2c下载完成: {}", task.output_path);
        } else {
            result.status = DownloadStatus::FAILED;
            result.error_message = aria2c_result.error_message;
            LOG_ERROR("Aria2c下载失败: {}", result.error_message);
        }

    } catch (const std::exception& e) {
        result.status = DownloadStatus::FAILED;
        result.error_message = e.what();
        LOG_ERROR("Aria2c下载过程中发生异常: {}", e.what());
    }

    return result;
}

std::string DownloaderCore::GenerateTaskId() {
    return "task_" + std::to_string(++task_counter_);
}

void DownloaderCore::UpdateProgress() {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (progress_callback_) {
        progress_callback_(GetProgress());
    }
}

void DownloaderCore::NotifyTaskCompletion(const DownloadResult& result) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (task_completion_callback_) {
        task_completion_callback_(result);
    }
}

std::string DownloaderCore::HandleFileConflict(const std::string& file_path) {
    return storage_manager_.HandleFileConflict(file_path, config_.file_conflict);
}

} // namespace download
} // namespace pixiv_downloader
