#!/bin/bash

echo "=== PixivTagDownloader 路径修复验证 ==="

# 检查配置文件
echo "1. 检查配置文件内容："
echo "   output_root_dir: $(grep 'output_root_dir:' test/config.yaml)"
echo "   artwork_path_template: $(grep 'artwork_path_template:' test/config.yaml)"

# 运行配置检查
echo ""
echo "2. 运行配置检查："
cd test && ./PixivTagDownloader --check-config

echo ""
echo "3. 检查test目录结构："
ls -la

echo ""
echo "=== 修复总结 ==="
echo "1. 修复了模板处理顺序：先处理条件模板，再处理普通变量"
echo "2. 改进了用户名处理：空用户名时使用 User_<uid> 格式"
echo "3. 修改了SanitizeFileName的默认值：从 'unknown_user' 改为 'unnamed'"
echo "4. 简化了路径模板：移除了不必要的 {series_title|No_Series} 层级"
echo ""
echo "预期的正确路径格式："
echo "  ./downloads/<uid>_<username>/<type>/<filename>"
echo "  例如：./downloads/28925283_クロソト/illust/20231027_123456789_作品标题.png"
