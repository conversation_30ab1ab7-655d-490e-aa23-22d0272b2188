# PixivTagDownloader 路径保存问题修复总结

## 问题描述

在test路径下发现程序保存的路径不正确，出现了以下问题：

1. **错误的路径结构**：
   - 实际路径：`test/unnamed/downloads/28925283_クロソト/illust/...`
   - 实际路径：`test/unknown_user/downloads/107837055_Novogradie/...`

2. **期望的路径结构**：
   - 应该是：`./downloads/28925283_クロソト/illust/...`

## 根本原因分析

### 1. 路径安全化问题
- `SanitizePath`函数对包含`./`的路径进行处理时，会将`./downloads`中的`./`视为非法字符
- `SanitizeFileName`函数将处理后的空字符串替换为默认值`"unnamed"`
- 导致`output_root_dir`的值`"./downloads"`被错误替换为`"unnamed"`

### 2. 模板处理顺序问题
- 原代码先处理普通变量，再处理条件模板
- 这会导致条件模板中的变量被提前替换，影响条件判断

### 3. 用户名处理问题
- 当用户信息获取失败时，`SanitizeFileName`返回`"unknown_user"`
- 缺乏更合理的默认值处理机制

## 修复方案

### 1. 修复路径安全化逻辑 (`src/storage/storage_manager.cpp`)

```cpp
std::string StorageManager::SanitizePath(const std::string& path) {
    // 对于包含相对路径前缀的路径，需要特殊处理
    std::string result = path;
    
    // 检查是否以 ./ 或 ../ 开头
    bool has_relative_prefix = false;
    std::string prefix;
    if (result.substr(0, 2) == "./") {
        has_relative_prefix = true;
        prefix = "./";
        result = result.substr(2);
    } else if (result.substr(0, 3) == "../") {
        has_relative_prefix = true;
        prefix = "../";
        result = result.substr(3);
    }
    
    // 分割路径组件并分别安全化
    // ... 处理逻辑 ...
    
    // 如果原来有相对路径前缀，重新添加
    if (has_relative_prefix) {
        sanitized_path = prefix + sanitized_path;
    }
    
    return sanitized_path;
}
```

### 2. 修复模板处理顺序 (`src/storage/storage_manager.cpp`)

```cpp
std::string StorageManager::ProcessTemplate(const std::string& template_str, 
                                           const TemplateVariables& variables) {
    std::string result = template_str;
    
    // 首先处理条件模板（如 {series_title|No_Series}）
    std::regex conditional_pattern(R"(\{([^|}]+)\|([^}]+)\})");
    // ... 条件模板处理逻辑 ...
    
    // 然后替换普通模板变量
    for (const auto& [key, value] : variables) {
        std::string placeholder = "{" + key + "}";
        result = utilities::StringUtils::ReplaceAll(result, placeholder, value);
    }
    
    return result;
}
```

### 3. 改进用户名处理 (`src/storage/storage_manager.cpp`)

```cpp
// 处理用户名：如果为空或获取失败，使用用户ID作为默认值
std::string username = user_info.username;
if (username.empty() && !user_info.uid.empty()) {
    username = "User_" + user_info.uid;
    LOG_WARN("用户名为空，使用默认值: {}", username);
}
variables["username"] = SanitizeFileName(username);
```

### 4. 修复C++17兼容性问题

- 将`map.contains()`替换为`map.find() != map.end()`
- 添加缺失的`<algorithm>`头文件

### 5. 简化配置文件路径模板

```yaml
paths:
  output_root_dir: "./downloads"
  artwork_path_template: "{output_root_dir}/{uid}_{username}/{type}/"
  # 移除了不必要的 {series_title|No_Series} 层级
```

## 修复验证

### 编译和测试
```bash
# 使用CMake编译
mkdir -p build && cd build
cmake ..
make -j$(nproc)

# 复制到test目录并运行诊断
cp build/PixivTagDownloader test/
cd test && ./PixivTagDownloader --diagnose
```

### 测试结果
```
🧪 路径生成测试:
  正常用户路径: ./downloads/87654321_测试用户/illust/_123456789_测试作品.jpg
  空用户名路径: ./downloads/87654321_User_87654321/illust/_123456789_测试作品.jpg
```

## 修复效果

1. ✅ **路径结构正确**：`output_root_dir`不再被错误替换
2. ✅ **用户名处理改进**：空用户名时使用`User_<uid>`格式
3. ✅ **模板处理优化**：条件模板处理顺序正确
4. ✅ **编译兼容性**：支持C++17标准
5. ✅ **配置简化**：移除不必要的路径层级

## 预期的正确路径格式

```
./downloads/<uid>_<username>/<type>/<filename>
例如：./downloads/28925283_クロソト/illust/20231027_123456789_作品标题.png
```

现在程序将按照配置文件中的模板正确生成文件保存路径，不再出现`unnamed`或`unknown_user`这样的错误路径。
