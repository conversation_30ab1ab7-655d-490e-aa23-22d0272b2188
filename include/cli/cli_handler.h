#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <CLI/CLI.hpp>
#include "config/config_manager.h"
#include "download/downloader_core.h"

namespace pixiv_downloader {

// 前向声明
namespace api {
    class PixivApiClient;
}

namespace cli {

/**
 * @brief 用户选择结构
 */
struct UserSelection {
    std::string uid;                                    // 用户ID
    std::vector<config::ArtworkType> artwork_types;    // 作品类型
    std::vector<std::string> tags;                      // 标签列表
    config::TagFilterLogic tag_logic;                  // 标签过滤逻辑
    bool download_all_works = false;                    // 是否下载所有作品
};

/**
 * @brief 命令行参数结构
 */
struct CommandLineArgs {
    std::string uid;
    std::vector<std::string> tags;
    std::string tag_logic = "or";
    bool all_works = false;
    std::string artwork_types = "all";
    std::string config_path = "config.yaml";
    std::string output_dir;
    std::string download_method;
    int concurrency = 0;
    std::string delay_range;
    std::string file_conflict;
    std::string log_level;
    std::string log_path;
    bool interactive = false;
    bool help = false;
    bool version = false;
    bool diagnose = false;
};

/**
 * @brief 进度显示器类
 */
class ProgressDisplay {
public:
    /**
     * @brief 构造函数
     */
    ProgressDisplay();

    /**
     * @brief 析构函数
     */
    ~ProgressDisplay();

    /**
     * @brief 开始显示进度
     */
    void Start();

    /**
     * @brief 停止显示进度
     */
    void Stop();

    /**
     * @brief 更新进度信息
     * 
     * @param progress 下载进度
     */
    void UpdateProgress(const download::DownloadProgress& progress);

    /**
     * @brief 显示任务完成信息
     * 
     * @param result 下载结果
     */
    void ShowTaskCompletion(const download::DownloadResult& result);

    /**
     * @brief 显示最终统计信息
     * 
     * @param results 所有下载结果
     */
    void ShowFinalStatistics(const std::vector<download::DownloadResult>& results);

    /**
     * @brief 设置是否显示详细信息
     * 
     * @param verbose 是否详细
     */
    void SetVerbose(bool verbose) { verbose_ = verbose; }

private:
    bool is_running_;
    bool verbose_;
    std::string last_display_;
    mutable std::mutex display_mutex_;

    /**
     * @brief 格式化进度条
     * 
     * @param progress 进度百分比
     * @param width 进度条宽度
     * @return std::string 进度条字符串
     */
    std::string FormatProgressBar(double progress, int width = 50);

    /**
     * @brief 格式化文件大小
     * 
     * @param bytes 字节数
     * @return std::string 格式化后的大小
     */
    std::string FormatFileSize(size_t bytes);

    /**
     * @brief 格式化时间
     * 
     * @param seconds 秒数
     * @return std::string 格式化后的时间
     */
    std::string FormatTime(double seconds);

    /**
     * @brief 清除当前行
     */
    void ClearCurrentLine();
};

/**
 * @brief CLI处理器类
 * 
 * 负责命令行参数解析、交互式界面和用户输入处理
 */
class CliHandler {
public:
    /**
     * @brief 构造函数
     *
     * @param config_manager 配置管理器引用
     */
    explicit CliHandler(config::ConfigManager& config_manager);

    /**
     * @brief 带API客户端的构造函数
     *
     * @param config_manager 配置管理器引用
     * @param api_client API客户端引用
     */
    CliHandler(config::ConfigManager& config_manager, api::PixivApiClient* api_client);

    /**
     * @brief 析构函数
     */
    ~CliHandler();

    /**
     * @brief 解析命令行参数
     * 
     * @param argc 参数数量
     * @param argv 参数数组
     * @return CommandLineArgs 解析后的参数
     */
    CommandLineArgs ParseCommandLine(int argc, char* argv[]);

    /**
     * @brief 运行交互式模式
     * 
     * @return UserSelection 用户选择
     */
    UserSelection RunInteractiveMode();

    /**
     * @brief 从命令行参数创建用户选择
     * 
     * @param args 命令行参数
     * @return UserSelection 用户选择
     */
    UserSelection CreateSelectionFromArgs(const CommandLineArgs& args);

    /**
     * @brief 显示帮助信息
     */
    void ShowHelp();

    /**
     * @brief 显示版本信息
     */
    void ShowVersion();

    /**
     * @brief 显示欢迎信息
     */
    void ShowWelcome();

    /**
     * @brief 确认用户选择
     * 
     * @param selection 用户选择
     * @return true 用户确认
     * @return false 用户取消
     */
    bool ConfirmSelection(const UserSelection& selection);

    /**
     * @brief 询问是否继续新任务
     * 
     * @return true 继续
     * @return false 退出
     */
    bool AskForNewTask();

    /**
     * @brief 设置进度回调
     * 
     * @param downloader 下载器引用
     */
    void SetupProgressCallback(download::DownloaderCore& downloader);

    /**
     * @brief 处理Ctrl-C信号
     * 
     * @param downloader 下载器引用
     */
    void SetupSignalHandlers(download::DownloaderCore& downloader);

    /**
     * @brief 显示错误信息
     * 
     * @param message 错误信息
     */
    void ShowError(const std::string& message);

    /**
     * @brief 显示警告信息
     * 
     * @param message 警告信息
     */
    void ShowWarning(const std::string& message);

    /**
     * @brief 显示信息
     *
     * @param message 信息内容
     */
    void ShowInfo(const std::string& message);

    /**
     * @brief 获取用户确认
     *
     * @param prompt 提示信息
     * @param default_yes 默认是否为是
     * @return true 用户确认
     * @return false 用户拒绝
     */
    bool GetUserConfirmation(const std::string& prompt, bool default_yes = true);

private:
    config::ConfigManager& config_manager_;
    api::PixivApiClient* api_client_;
    std::unique_ptr<CLI::App> app_;
    std::unique_ptr<ProgressDisplay> progress_display_;
    std::atomic<bool> signal_received_;
    std::atomic<int> signal_count_;

    /**
     * @brief 获取用户输入
     * 
     * @param prompt 提示信息
     * @param default_value 默认值
     * @return std::string 用户输入
     */
    std::string GetUserInput(const std::string& prompt, const std::string& default_value = "");

    /**
     * @brief 获取用户选择（多选）
     * 
     * @param prompt 提示信息
     * @param options 选项列表
     * @return std::vector<int> 选择的索引列表
     */
    std::vector<int> GetUserChoice(const std::string& prompt, const std::vector<std::string>& options);

    /**
     * @brief 选择作品类型
     * 
     * @return std::vector<config::ArtworkType> 选择的作品类型
     */
    std::vector<config::ArtworkType> SelectArtworkTypes();

    /**
     * @brief 输入用户ID
     * 
     * @return std::string 用户ID
     */
    std::string InputUserId();

    /**
     * @brief 选择下载方式
     * 
     * @return int 下载方式选择
     */
    int SelectDownloadMethod();

    /**
     * @brief 手动输入标签
     * 
     * @return std::vector<std::string> 标签列表
     */
    std::vector<std::string> InputTagsManually();

    /**
     * @brief 从用户作品中选择标签
     * 
     * @param uid 用户ID
     * @return std::vector<std::string> 选择的标签
     */
    std::vector<std::string> SelectTagsFromUser(const std::string& uid);

    /**
     * @brief 选择标签过滤逻辑
     * 
     * @return config::TagFilterLogic 过滤逻辑
     */
    config::TagFilterLogic SelectTagFilterLogic();

    /**
     * @brief 显示标签列表（分页）
     * 
     * @param tags 标签列表
     * @param page_size 每页大小
     * @return std::vector<std::string> 选择的标签
     */
    std::vector<std::string> DisplayTagsWithPagination(const std::vector<std::string>& tags, 
                                                       int page_size = 20);

    /**
     * @brief 搜索标签
     * 
     * @param tags 标签列表
     * @param keyword 关键词
     * @return std::vector<std::string> 匹配的标签
     */
    std::vector<std::string> SearchTags(const std::vector<std::string>& tags, 
                                       const std::string& keyword);

    /**
     * @brief 验证用户ID格式
     * 
     * @param uid 用户ID
     * @return true 格式正确
     * @return false 格式错误
     */
    bool ValidateUserId(const std::string& uid);

    /**
     * @brief 解析延迟范围字符串
     * 
     * @param delay_str 延迟字符串（如"1-3"）
     * @return std::pair<int, int> 最小和最大延迟
     */
    std::pair<int, int> ParseDelayRange(const std::string& delay_str);

    /**
     * @brief 信号处理函数
     * 
     * @param signal 信号编号
     */
    static void SignalHandler(int signal);

    // 静态成员用于信号处理
    static CliHandler* instance_;
    static download::DownloaderCore* downloader_instance_;
};

} // namespace cli
} // namespace pixiv_downloader
