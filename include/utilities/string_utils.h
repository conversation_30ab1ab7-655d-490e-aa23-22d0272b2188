#pragma once

#include <string>
#include <vector>
#include <algorithm>
#include <cctype>
#include <locale>
#include <sstream>
#include <memory>

namespace pixiv_downloader {
namespace utilities {

/**
 * @brief 字符串工具类
 * 
 * 提供各种字符串处理功能，包括文件名安全化、编码转换等
 */
class StringUtils {
public:
    /**
     * @brief 去除字符串两端的空白字符
     * 
     * @param str 输入字符串
     * @return std::string 处理后的字符串
     */
    static std::string Trim(const std::string& str);

    /**
     * @brief 去除字符串左端的空白字符
     * 
     * @param str 输入字符串
     * @return std::string 处理后的字符串
     */
    static std::string TrimLeft(const std::string& str);

    /**
     * @brief 去除字符串右端的空白字符
     * 
     * @param str 输入字符串
     * @return std::string 处理后的字符串
     */
    static std::string TrimRight(const std::string& str);

    /**
     * @brief 将字符串转换为小写
     * 
     * @param str 输入字符串
     * @return std::string 小写字符串
     */
    static std::string ToLower(const std::string& str);

    /**
     * @brief 将字符串转换为大写
     * 
     * @param str 输入字符串
     * @return std::string 大写字符串
     */
    static std::string ToUpper(const std::string& str);

    /**
     * @brief 分割字符串
     * 
     * @param str 输入字符串
     * @param delimiter 分隔符
     * @return std::vector<std::string> 分割后的字符串数组
     */
    static std::vector<std::string> Split(const std::string& str, const std::string& delimiter);

    /**
     * @brief 连接字符串数组
     * 
     * @param strings 字符串数组
     * @param delimiter 分隔符
     * @return std::string 连接后的字符串
     */
    static std::string Join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief 替换字符串中的所有匹配项
     * 
     * @param str 输入字符串
     * @param from 要替换的子字符串
     * @param to 替换为的字符串
     * @return std::string 替换后的字符串
     */
    static std::string ReplaceAll(const std::string& str, const std::string& from, const std::string& to);

    /**
     * @brief 检查字符串是否以指定前缀开始
     * 
     * @param str 输入字符串
     * @param prefix 前缀
     * @return true 以前缀开始
     * @return false 不以前缀开始
     */
    static bool StartsWith(const std::string& str, const std::string& prefix);

    /**
     * @brief 检查字符串是否以指定后缀结束
     * 
     * @param str 输入字符串
     * @param suffix 后缀
     * @return true 以后缀结束
     * @return false 不以后缀结束
     */
    static bool EndsWith(const std::string& str, const std::string& suffix);

    /**
     * @brief 将字符串转换为文件系统安全的名称
     * 
     * 移除或替换文件系统中非法的字符
     * 
     * @param str 输入字符串
     * @param replacement 替换字符（默认为下划线）
     * @return std::string 安全的文件名
     */
    static std::string SanitizeFileName(const std::string& str, const std::string& replacement = "_");

    /**
     * @brief 截断字符串到指定长度
     * 
     * @param str 输入字符串
     * @param max_length 最大长度
     * @param suffix 截断后添加的后缀（如"..."）
     * @return std::string 截断后的字符串
     */
    static std::string Truncate(const std::string& str, size_t max_length, const std::string& suffix = "...");

    /**
     * @brief 生成字符串的哈希值（用于文件名唯一性）
     * 
     * @param str 输入字符串
     * @return std::string 哈希值的十六进制表示
     */
    static std::string GenerateHash(const std::string& str);

    /**
     * @brief URL编码
     * 
     * @param str 输入字符串
     * @return std::string URL编码后的字符串
     */
    static std::string UrlEncode(const std::string& str);

    /**
     * @brief URL解码
     * 
     * @param str 输入字符串
     * @return std::string URL解码后的字符串
     */
    static std::string UrlDecode(const std::string& str);

    /**
     * @brief 检查字符串是否为数字
     * 
     * @param str 输入字符串
     * @return true 是数字
     * @return false 不是数字
     */
    static bool IsNumeric(const std::string& str);

    /**
     * @brief 格式化字符串（类似printf）
     * 
     * @tparam Args 参数类型
     * @param format 格式字符串
     * @param args 参数
     * @return std::string 格式化后的字符串
     */
    template<typename... Args>
    static std::string Format(const std::string& format, Args... args) {
        int size_s = std::snprintf(nullptr, 0, format.c_str(), args...) + 1;
        if (size_s <= 0) {
            return "";
        }
        auto size = static_cast<size_t>(size_s);
        std::unique_ptr<char[]> buf(new char[size]);
        std::snprintf(buf.get(), size, format.c_str(), args...);
        return std::string(buf.get(), buf.get() + size - 1);
    }

private:
    // 文件系统非法字符集合
    static const std::string ILLEGAL_FILENAME_CHARS;
};

} // namespace utilities
} // namespace pixiv_downloader
