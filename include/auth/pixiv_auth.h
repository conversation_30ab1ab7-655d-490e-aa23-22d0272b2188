#pragma once

#include <string>
#include <map>
#include <vector>
#include <memory>

namespace pixiv_downloader {

// 前向声明
namespace config {
    struct AuthConfig;
}

namespace auth {

/**
 * @brief 认证状态枚举
 */
enum class AuthStatus {
    NOT_AUTHENTICATED,  // 未认证
    AUTHENTICATED,      // 已认证
    EXPIRED,           // 已过期
    INVALID,           // 无效
    INSUFFICIENT_PERMISSIONS // 权限不足
};

/**
 * @brief Cookie信息结构
 */
struct CookieInfo {
    std::string name;
    std::string value;
    std::string domain;
    std::string path;
    bool secure = false;
    bool http_only = false;
};

/**
 * @brief 用户信息结构
 */
struct UserInfo {
    std::string user_id;
    std::string username;
    std::string email;
    bool is_premium = false;
    bool can_view_r18 = false;
    std::string profile_image_url;
};

/**
 * @brief Pixiv认证管理器类
 * 
 * 负责处理Pixiv的Cookie认证、验证和用户信息获取
 */
class PixivAuth {
public:
    /**
     * @brief 构造函数
     */
    PixivAuth();

    /**
     * @brief 带配置的构造函数
     *
     * @param auth_config 认证配置
     */
    explicit PixivAuth(const config::AuthConfig& auth_config);

    /**
     * @brief 析构函数
     */
    ~PixivAuth() = default;

    /**
     * @brief 设置Cookie字符串
     * 
     * @param cookie_string Cookie字符串（格式：key1=value1; key2=value2; ...）
     * @return true 设置成功
     * @return false 设置失败
     */
    bool SetCookieString(const std::string& cookie_string);

    /**
     * @brief 解析Cookie字符串
     * 
     * @param cookie_string Cookie字符串
     * @return std::map<std::string, std::string> 解析后的Cookie键值对
     */
    static std::map<std::string, std::string> ParseCookieString(const std::string& cookie_string);

    /**
     * @brief 验证Cookie有效性
     * 
     * 通过访问需要认证的API端点来验证Cookie是否有效
     * 
     * @return AuthStatus 认证状态
     */
    AuthStatus ValidateCookie();

    /**
     * @brief 获取当前用户信息
     * 
     * @return UserInfo 用户信息，失败时返回空的UserInfo
     */
    UserInfo GetCurrentUserInfo();

    /**
     * @brief 检查是否有查看R18内容的权限
     * 
     * @return true 有权限
     * @return false 无权限
     */
    bool CanViewR18Content();

    /**
     * @brief 获取认证状态
     * 
     * @return AuthStatus 当前认证状态
     */
    AuthStatus GetAuthStatus() const { return auth_status_; }

    /**
     * @brief 获取Cookie字符串（用于HTTP请求）
     * 
     * @return std::string Cookie字符串
     */
    std::string GetCookieString() const;

    /**
     * @brief 获取Cookie映射
     * 
     * @return const std::map<std::string, std::string>& Cookie键值对映射
     */
    const std::map<std::string, std::string>& GetCookies() const { return cookies_; }

    /**
     * @brief 获取特定Cookie值
     * 
     * @param name Cookie名称
     * @return std::string Cookie值，不存在返回空字符串
     */
    std::string GetCookieValue(const std::string& name) const;

    /**
     * @brief 检查是否包含特定Cookie
     * 
     * @param name Cookie名称
     * @return true 包含
     * @return false 不包含
     */
    bool HasCookie(const std::string& name) const;

    /**
     * @brief 清除所有Cookie
     */
    void ClearCookies();

    /**
     * @brief 获取用户信息
     * 
     * @return const UserInfo& 用户信息引用
     */
    const UserInfo& GetUserInfo() const { return user_info_; }

    /**
     * @brief 检查Cookie是否包含必要的认证信息
     * 
     * @return true 包含必要信息
     * @return false 缺少必要信息
     */
    bool HasRequiredCookies() const;

    /**
     * @brief 获取认证状态的字符串描述
     * 
     * @param status 认证状态
     * @return std::string 状态描述
     */
    static std::string AuthStatusToString(AuthStatus status);

    /**
     * @brief 验证Cookie格式是否正确
     * 
     * @param cookie_string Cookie字符串
     * @return true 格式正确
     * @return false 格式错误
     */
    static bool ValidateCookieFormat(const std::string& cookie_string);

    /**
     * @brief 从Cookie字符串中提取域名
     * 
     * @param cookie_string Cookie字符串
     * @return std::string 域名
     */
    static std::string ExtractDomainFromCookie(const std::string& cookie_string);

    /**
     * @brief 检查Cookie是否过期
     * 
     * @param cookie_string Cookie字符串
     * @return true 已过期
     * @return false 未过期
     */
    static bool IsCookieExpired(const std::string& cookie_string);

private:
    std::map<std::string, std::string> cookies_;
    AuthStatus auth_status_;
    UserInfo user_info_;
    std::string original_cookie_string_;
    const config::AuthConfig* auth_config_;

    /**
     * @brief 更新认证状态
     * 
     * @param status 新的认证状态
     */
    void UpdateAuthStatus(AuthStatus status);

    /**
     * @brief 重置用户信息
     */
    void ResetUserInfo();

    /**
     * @brief 验证必要的Cookie是否存在
     * 
     * @return true 必要Cookie存在
     * @return false 缺少必要Cookie
     */
    bool ValidateRequiredCookies() const;

    // Pixiv认证相关的常量
    static const std::vector<std::string> REQUIRED_COOKIE_NAMES;
    static const std::string PIXIV_DOMAIN;
    static const std::string AUTH_TEST_ENDPOINT;
};

} // namespace auth
} // namespace pixiv_downloader
