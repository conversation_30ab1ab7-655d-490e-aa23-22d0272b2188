#pragma once

#include <string>
#include <vector>
#include <queue>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <memory>
#include <functional>
#include <future>
#include "config/config_manager.h"
#include "api/pixiv_api_client.h"
#include "storage/storage_manager.h"

namespace pixiv_downloader {
namespace download {

// 前向声明
class Aria2cDownloader;

/**
 * @brief 下载任务结构
 */
struct DownloadTask {
    std::string url;                    // 下载URL
    std::string output_path;            // 输出路径
    api::ArtworkInfo artwork_info;      // 作品信息
    int page_index = 0;                 // 页面索引（多页作品）
    std::string task_id;                // 任务ID
    int priority = 0;                   // 优先级
    int retry_count = 0;                // 重试次数
};

/**
 * @brief 下载状态枚举
 */
enum class DownloadStatus {
    PENDING,     // 等待中
    DOWNLOADING, // 下载中
    COMPLETED,   // 已完成
    FAILED,      // 失败
    CANCELLED,   // 已取消
    SKIPPED      // 已跳过
};

/**
 * @brief 下载结果结构
 */
struct DownloadResult {
    std::string task_id;
    DownloadStatus status;
    std::string file_path;
    size_t file_size = 0;
    std::string error_message;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point end_time;
};

/**
 * @brief 下载进度信息结构
 */
struct DownloadProgress {
    int total_tasks = 0;
    int completed_tasks = 0;
    int failed_tasks = 0;
    int skipped_tasks = 0;
    size_t total_bytes = 0;
    size_t downloaded_bytes = 0;
    double overall_progress = 0.0;
    std::string current_file;
    double current_file_progress = 0.0;
};

/**
 * @brief 进度回调函数类型
 */
using ProgressCallback = std::function<void(const DownloadProgress&)>;

/**
 * @brief 任务完成回调函数类型
 */
using TaskCompletionCallback = std::function<void(const DownloadResult&)>;

/**
 * @brief 下载核心管理器类
 * 
 * 实现生产者-消费者模型的并发下载系统
 */
class DownloaderCore {
public:
    /**
     * @brief 构造函数
     * 
     * @param config 配置引用
     * @param api_client API客户端引用
     * @param storage_manager 存储管理器引用
     */
    DownloaderCore(const config::Config& config, 
                   api::PixivApiClient& api_client,
                   storage::StorageManager& storage_manager);

    /**
     * @brief 析构函数
     */
    ~DownloaderCore();

    /**
     * @brief 初始化下载器
     * 
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool Initialize();

    /**
     * @brief 启动下载器
     * 
     * @param worker_count 工作线程数量
     * @return true 启动成功
     * @return false 启动失败
     */
    bool Start(int worker_count = 0);

    /**
     * @brief 停止下载器
     * 
     * @param wait_for_completion 是否等待当前任务完成
     */
    void Stop(bool wait_for_completion = true);

    /**
     * @brief 添加下载任务
     * 
     * @param task 下载任务
     * @return std::string 任务ID
     */
    std::string AddTask(const DownloadTask& task);

    /**
     * @brief 批量添加下载任务
     * 
     * @param tasks 下载任务列表
     * @return std::vector<std::string> 任务ID列表
     */
    std::vector<std::string> AddTasks(const std::vector<DownloadTask>& tasks);

    /**
     * @brief 取消任务
     * 
     * @param task_id 任务ID
     * @return true 取消成功
     * @return false 取消失败
     */
    bool CancelTask(const std::string& task_id);

    /**
     * @brief 取消所有任务
     */
    void CancelAllTasks();

    /**
     * @brief 等待所有任务完成
     * 
     * @param timeout_ms 超时时间（毫秒），0表示无限等待
     * @return true 所有任务完成
     * @return false 超时
     */
    bool WaitForCompletion(int timeout_ms = 0);

    /**
     * @brief 获取下载进度
     * 
     * @return DownloadProgress 下载进度信息
     */
    DownloadProgress GetProgress() const;

    /**
     * @brief 获取任务结果
     * 
     * @param task_id 任务ID
     * @return DownloadResult 下载结果
     */
    DownloadResult GetTaskResult(const std::string& task_id) const;

    /**
     * @brief 获取所有任务结果
     * 
     * @return std::vector<DownloadResult> 所有任务结果
     */
    std::vector<DownloadResult> GetAllResults() const;

    /**
     * @brief 设置进度回调函数
     * 
     * @param callback 回调函数
     */
    void SetProgressCallback(ProgressCallback callback);

    /**
     * @brief 设置任务完成回调函数
     * 
     * @param callback 回调函数
     */
    void SetTaskCompletionCallback(TaskCompletionCallback callback);

    /**
     * @brief 检查是否正在运行
     * 
     * @return true 正在运行
     * @return false 未运行
     */
    bool IsRunning() const { return is_running_; }

    /**
     * @brief 获取队列中的任务数量
     * 
     * @return size_t 任务数量
     */
    size_t GetQueueSize() const;

    /**
     * @brief 获取活跃的工作线程数量
     * 
     * @return int 工作线程数量
     */
    int GetActiveWorkerCount() const { return active_workers_; }

    /**
     * @brief 清除已完成的任务结果
     */
    void ClearCompletedResults();

    /**
     * @brief 暂停下载
     */
    void Pause();

    /**
     * @brief 恢复下载
     */
    void Resume();

    /**
     * @brief 检查是否已暂停
     * 
     * @return true 已暂停
     * @return false 未暂停
     */
    bool IsPaused() const { return is_paused_; }

private:
    const config::Config& config_;
    api::PixivApiClient& api_client_;
    storage::StorageManager& storage_manager_;
    std::unique_ptr<Aria2cDownloader> aria2c_downloader_;

    // 线程同步
    std::queue<DownloadTask> task_queue_;
    mutable std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::condition_variable completion_condition_;

    // 工作线程
    std::vector<std::thread> worker_threads_;
    std::atomic<bool> is_running_;
    std::atomic<bool> should_stop_;
    std::atomic<bool> is_paused_;
    std::atomic<int> active_workers_;

    // 任务管理
    std::map<std::string, DownloadResult> task_results_;
    mutable std::mutex results_mutex_;
    std::atomic<int> task_counter_;

    // 进度跟踪
    std::atomic<int> total_tasks_;
    std::atomic<int> completed_tasks_;
    std::atomic<int> failed_tasks_;
    std::atomic<int> skipped_tasks_;
    std::atomic<size_t> total_bytes_;
    std::atomic<size_t> downloaded_bytes_;

    // 回调函数
    ProgressCallback progress_callback_;
    TaskCompletionCallback task_completion_callback_;
    mutable std::mutex callback_mutex_;

    /**
     * @brief 工作线程函数
     * 
     * @param worker_id 工作线程ID
     */
    void WorkerThread(int worker_id);

    /**
     * @brief 处理单个下载任务
     * 
     * @param task 下载任务
     * @return DownloadResult 下载结果
     */
    DownloadResult ProcessTask(const DownloadTask& task);

    /**
     * @brief 执行直接下载
     * 
     * @param task 下载任务
     * @return DownloadResult 下载结果
     */
    DownloadResult PerformDirectDownload(const DownloadTask& task);

    /**
     * @brief 执行Aria2c下载
     * 
     * @param task 下载任务
     * @return DownloadResult 下载结果
     */
    DownloadResult PerformAria2cDownload(const DownloadTask& task);

    /**
     * @brief 生成任务ID
     * 
     * @return std::string 唯一任务ID
     */
    std::string GenerateTaskId();

    /**
     * @brief 更新进度信息
     */
    void UpdateProgress();

    /**
     * @brief 通知任务完成
     * 
     * @param result 任务结果
     */
    void NotifyTaskCompletion(const DownloadResult& result);

    /**
     * @brief 检查文件是否应该跳过
     * 
     * @param file_path 文件路径
     * @return true 应该跳过
     * @return false 不应该跳过
     */
    bool ShouldSkipFile(const std::string& file_path);

    /**
     * @brief 处理文件冲突
     * 
     * @param file_path 文件路径
     * @return std::string 处理后的文件路径
     */
    std::string HandleFileConflict(const std::string& file_path);

    /**
     * @brief 验证下载的文件
     * 
     * @param file_path 文件路径
     * @param expected_size 期望大小
     * @return true 验证成功
     * @return false 验证失败
     */
    bool ValidateDownloadedFile(const std::string& file_path, size_t expected_size);
};

} // namespace download
} // namespace pixiv_downloader
