#pragma once

#include <string>
#include <vector>
#include <map>
#include <functional>

namespace pixiv_downloader {
namespace download {

/**
 * @brief Aria2c下载任务结构
 */
struct Aria2cTask {
    std::string url;
    std::string output_path;
    std::string referer;
    std::string user_agent;
    std::string cookie;
    std::map<std::string, std::string> headers;
};

/**
 * @brief Aria2c下载结果
 */
struct Aria2cResult {
    bool success = false;
    std::string error_message;
    size_t file_size = 0;
    double download_time = 0.0;
};

/**
 * @brief Aria2c下载器类
 * 
 * 负责使用外部aria2c程序进行文件下载
 */
class Aria2cDownloader {
public:
    /**
     * @brief 构造函数
     * 
     * @param aria2c_path aria2c可执行文件路径
     * @param default_options 默认选项
     */
    explicit Aria2cDownloader(const std::string& aria2c_path = "aria2c", 
                             const std::string& default_options = "");

    /**
     * @brief 析构函数
     */
    ~Aria2cDownloader();

    /**
     * @brief 检查aria2c是否可用
     * 
     * @return true 可用
     * @return false 不可用
     */
    bool IsAvailable() const;

    /**
     * @brief 下载单个文件
     * 
     * @param task 下载任务
     * @return Aria2cResult 下载结果
     */
    Aria2cResult DownloadFile(const Aria2cTask& task);

    /**
     * @brief 批量下载文件
     * 
     * @param tasks 下载任务列表
     * @return std::vector<Aria2cResult> 下载结果列表
     */
    std::vector<Aria2cResult> DownloadFiles(const std::vector<Aria2cTask>& tasks);

    /**
     * @brief 设置进度回调
     * 
     * @param callback 进度回调函数
     */
    void SetProgressCallback(std::function<void(const std::string&, double)> callback);

    /**
     * @brief 设置aria2c选项
     * 
     * @param options 选项字符串
     */
    void SetOptions(const std::string& options);

    /**
     * @brief 获取aria2c版本信息
     * 
     * @return std::string 版本信息
     */
    std::string GetVersion() const;

private:
    std::string aria2c_path_;
    std::string default_options_;
    std::function<void(const std::string&, double)> progress_callback_;

    /**
     * @brief 创建aria2c输入文件
     * 
     * @param tasks 下载任务列表
     * @return std::string 输入文件路径
     */
    std::string CreateInputFile(const std::vector<Aria2cTask>& tasks);

    /**
     * @brief 执行aria2c命令
     * 
     * @param input_file 输入文件路径
     * @param output_dir 输出目录
     * @return bool 执行是否成功
     */
    bool ExecuteAria2c(const std::string& input_file, const std::string& output_dir);

    /**
     * @brief 解析aria2c输出
     * 
     * @param output aria2c输出
     * @return std::vector<Aria2cResult> 解析结果
     */
    std::vector<Aria2cResult> ParseOutput(const std::string& output);

    /**
     * @brief 清理临时文件
     * 
     * @param file_path 文件路径
     */
    void CleanupTempFile(const std::string& file_path);

    /**
     * @brief 转义shell参数
     * 
     * @param arg 参数
     * @return std::string 转义后的参数
     */
    std::string EscapeShellArg(const std::string& arg) const;
};

} // namespace download
} // namespace pixiv_downloader
