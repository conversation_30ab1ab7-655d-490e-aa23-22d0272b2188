#pragma once

#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <chrono>
#include "config/config_manager.h"
#include "auth/pixiv_auth.h"
#include "api/pixiv_api_client.h"
#include "download/downloader_core.h"
#include "storage/storage_manager.h"
#include "cli/cli_handler.h"

namespace pixiv_downloader {
namespace core {

/**
 * @brief 应用程序退出代码枚举
 */
enum class ExitCode {
    SUCCESS = 0,                    // 成功
    CONFIG_ERROR = 1,               // 配置错误
    AUTH_ERROR = 2,                 // 认证错误
    NETWORK_ERROR = 3,              // 网络错误
    STORAGE_ERROR = 4,              // 存储错误
    USER_CANCELLED = 5,             // 用户取消
    INVALID_ARGUMENTS = 6,          // 无效参数
    RUNTIME_ERROR = 7,              // 运行时错误
    UNKNOWN_ERROR = 99              // 未知错误
};

/**
 * @brief 应用程序状态枚举
 */
enum class AppState {
    INITIALIZING,   // 初始化中
    READY,          // 就绪
    RUNNING,        // 运行中
    PAUSED,         // 暂停
    STOPPING,       // 停止中
    STOPPED,        // 已停止
    ERROR           // 错误状态
};

/**
 * @brief 任务执行结果结构
 */
struct TaskExecutionResult {
    bool success = false;
    int total_artworks = 0;
    int downloaded_artworks = 0;
    int failed_artworks = 0;
    int skipped_artworks = 0;
    size_t total_bytes = 0;
    std::string error_message;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point end_time;
    std::vector<download::DownloadResult> failed_tasks; // 失败的任务列表
};

/**
 * @brief 主控制器类
 * 
 * 协调整个应用程序的流程，连接各个模块
 */
class MainController {
public:
    /**
     * @brief 构造函数
     */
    MainController();

    /**
     * @brief 析构函数
     */
    ~MainController();

    /**
     * @brief 运行应用程序
     * 
     * @param argc 命令行参数数量
     * @param argv 命令行参数数组
     * @return ExitCode 退出代码
     */
    ExitCode Run(int argc, char* argv[]);

    /**
     * @brief 初始化应用程序
     *
     * @param config_path 配置文件路径
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool Initialize(const std::string& config_path = "config.yaml");

    /**
     * @brief 应用命令行参数覆盖配置
     *
     * @param args 命令行参数
     */
    void ApplyCommandLineOverrides(const cli::CommandLineArgs& args);

    /**
     * @brief 关闭应用程序
     */
    void Shutdown();

    /**
     * @brief 执行下载任务
     * 
     * @param selection 用户选择
     * @return TaskExecutionResult 执行结果
     */
    TaskExecutionResult ExecuteDownloadTask(const cli::UserSelection& selection);

    /**
     * @brief 获取当前应用程序状态
     * 
     * @return AppState 应用程序状态
     */
    AppState GetState() const { return app_state_; }

    /**
     * @brief 检查是否正在运行
     * 
     * @return true 正在运行
     * @return false 未运行
     */
    bool IsRunning() const { return app_state_ == AppState::RUNNING; }

    /**
     * @brief 暂停执行
     */
    void Pause();

    /**
     * @brief 恢复执行
     */
    void Resume();

    /**
     * @brief 停止执行
     */
    void Stop();

    /**
     * @brief 获取最后一次错误信息
     * 
     * @return std::string 错误信息
     */
    std::string GetLastError() const { return last_error_; }

private:
    // 核心组件
    std::unique_ptr<config::ConfigManager> config_manager_;
    std::unique_ptr<auth::PixivAuth> pixiv_auth_;
    std::unique_ptr<api::PixivApiClient> api_client_;
    std::unique_ptr<download::DownloaderCore> downloader_;
    std::unique_ptr<storage::StorageManager> storage_manager_;
    std::unique_ptr<cli::CliHandler> cli_handler_;

    // 状态管理
    std::atomic<AppState> app_state_;
    std::string last_error_;
    bool initialized_;

    /**
     * @brief 设置应用程序状态
     * 
     * @param state 新状态
     */
    void SetState(AppState state);

    /**
     * @brief 设置错误信息
     * 
     * @param error 错误信息
     */
    void SetError(const std::string& error);

    /**
     * @brief 验证配置
     * 
     * @return true 配置有效
     * @return false 配置无效
     */
    bool ValidateConfiguration();

    /**
     * @brief 初始化日志系统
     * 
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool InitializeLogging();

    /**
     * @brief 验证认证
     * 
     * @return true 认证成功
     * @return false 认证失败
     */
    bool ValidateAuthentication();

    /**
     * @brief 初始化存储系统
     * 
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool InitializeStorage();

    /**
     * @brief 运行交互式模式
     * 
     * @return ExitCode 退出代码
     */
    ExitCode RunInteractiveMode();

    /**
     * @brief 运行命令行模式
     *
     * @param args 命令行参数
     * @return ExitCode 退出代码
     */
    ExitCode RunCommandLineMode(const cli::CommandLineArgs& args);

    /**
     * @brief 运行诊断模式
     *
     * @param args 命令行参数
     * @return ExitCode 退出代码
     */
    ExitCode RunDiagnosticMode(const cli::CommandLineArgs& args);

    /**
     * @brief 获取用户信息
     * 
     * @param uid 用户ID
     * @return api::UserInfo 用户信息
     */
    api::UserInfo GetUserInfo(const std::string& uid);

    /**
     * @brief 获取用户作品列表
     * 
     * @param uid 用户ID
     * @param selection 用户选择
     * @return std::vector<api::ArtworkInfo> 作品信息列表
     */
    std::vector<api::ArtworkInfo> GetUserArtworks(const std::string& uid, 
                                                  const cli::UserSelection& selection);

    /**
     * @brief 过滤作品
     * 
     * @param artworks 作品列表
     * @param selection 用户选择
     * @return std::vector<api::ArtworkInfo> 过滤后的作品列表
     */
    std::vector<api::ArtworkInfo> FilterArtworks(const std::vector<api::ArtworkInfo>& artworks,
                                                 const cli::UserSelection& selection);

    /**
     * @brief 应用标签过滤
     * 
     * @param artwork 作品信息
     * @param tags 标签列表
     * @param logic 过滤逻辑
     * @return true 通过过滤
     * @return false 未通过过滤
     */
    bool ApplyTagFilter(const api::ArtworkInfo& artwork,
                       const std::vector<std::string>& tags,
                       config::TagFilterLogic logic);

    /**
     * @brief 应用类型过滤
     * 
     * @param artwork 作品信息
     * @param types 类型列表
     * @return true 通过过滤
     * @return false 未通过过滤
     */
    bool ApplyTypeFilter(const api::ArtworkInfo& artwork,
                        const std::vector<config::ArtworkType>& types);

    /**
     * @brief 创建下载任务
     * 
     * @param artworks 作品列表
     * @param user_info 用户信息
     * @return std::vector<download::DownloadTask> 下载任务列表
     */
    std::vector<download::DownloadTask> CreateDownloadTasks(
        const std::vector<api::ArtworkInfo>& artworks,
        const api::UserInfo& user_info);

    /**
     * @brief 为单个作品创建下载任务
     * 
     * @param artwork 作品信息
     * @param user_info 用户信息
     * @return std::vector<download::DownloadTask> 下载任务列表
     */
    std::vector<download::DownloadTask> CreateTasksForArtwork(
        const api::ArtworkInfo& artwork,
        const api::UserInfo& user_info);

    /**
     * @brief 获取用户所有标签
     * 
     * @param uid 用户ID
     * @return std::vector<std::string> 标签列表
     */
    std::vector<std::string> GetUserTags(const std::string& uid);

    /**
     * @brief 处理应用程序异常
     * 
     * @param e 异常对象
     * @return ExitCode 退出代码
     */
    ExitCode HandleException(const std::exception& e);

    /**
     * @brief 清理资源
     */
    void CleanupResources();

    /**
     * @brief 显示执行摘要
     *
     * @param result 执行结果
     */
    void ShowExecutionSummary(const TaskExecutionResult& result);

    /**
     * @brief 处理失败任务重试
     *
     * @param result 执行结果
     * @param selection 用户选择
     * @param user_info 用户信息
     * @return bool 是否进行了重试
     */
    bool HandleFailedTasksRetry(TaskExecutionResult& result,
                               const cli::UserSelection& selection,
                               const api::UserInfo& user_info);

    /**
     * @brief 将退出代码转换为字符串
     * 
     * @param code 退出代码
     * @return std::string 代码描述
     */
    static std::string ExitCodeToString(ExitCode code);

    /**
     * @brief 将应用程序状态转换为字符串
     * 
     * @param state 应用程序状态
     * @return std::string 状态描述
     */
    static std::string AppStateToString(AppState state);
};

} // namespace core
} // namespace pixiv_downloader
