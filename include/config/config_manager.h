#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <yaml-cpp/yaml.h>
#include "utilities/logger.h"

namespace pixiv_downloader {
namespace config {

/**
 * @brief 下载方法枚举
 */
enum class DownloadMethod {
    DIRECT,  // 直接下载
    ARIA2C   // 使用aria2c
};

/**
 * @brief 文件冲突处理策略枚举
 */
enum class FileConflictStrategy {
    SKIP,      // 跳过
    OVERWRITE, // 覆盖
    RENAME     // 重命名
};

/**
 * @brief 标签过滤逻辑枚举
 */
enum class TagFilterLogic {
    AND, // 所有标签都必须匹配
    OR,  // 任意标签匹配
    NOT  // 排除包含指定标签的作品
};

/**
 * @brief 作品类型枚举
 */
enum class ArtworkType {
    ILLUST, // 插画
    MANGA,  // 漫画
    NOVEL,  // 小说
    ALL     // 全部
};

/**
 * @brief HTTP配置结构
 */
struct HttpConfig {
    std::string user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
    std::string referer = "https://www.pixiv.net/";
    std::map<std::string, std::string> custom_headers;
    int connection_timeout = 30;  // 连接超时（秒）
    int read_timeout = 60;        // 读取超时（秒）
    int max_retries = 3;          // 最大重试次数
    int retry_delay = 2;          // 重试延迟（秒）
};

/**
 * @brief 延迟配置结构
 */
struct DelayConfig {
    int min_delay = 1; // 最小延迟（秒）
    int max_delay = 3; // 最大延迟（秒）
};

/**
 * @brief 认证配置结构
 */
struct AuthConfig {
    bool skip_online_validation = false;  // 跳过在线Cookie验证
    bool allow_offline_mode = true;       // 允许离线模式
    int validation_timeout = 30;          // 验证超时时间（秒）
    bool require_r18_permission = false;  // 是否要求R18权限
    bool strict_cookie_validation = true; // 严格Cookie验证
};

/**
 * @brief 路径模板配置结构
 */
struct PathTemplateConfig {
    std::string output_root_dir = "./downloads";
    std::string artwork_path_template = "{output_root_dir}/{uid}_{username}/{type}/";
    std::string single_image_naming_template = "{upload_date}_{pid}_p0_{title}{ext}";
    std::string multi_image_subfolder_naming_template = "{pid}_{title}/";
    std::string multi_image_file_naming_template = "{page_index}_{title}{ext}";
    std::string novel_path_template = "{output_root_dir}/{uid}_{username}/Novel/{series_title|No_Series}/";
    std::string novel_naming_template = "{upload_date}_{pid}_{title}.txt";
    std::string metadata_filename_template = "{pid}_info.txt";
    std::string page_index_format = "p%02d"; // 页码格式
    std::string date_format = "%Y%m%d";      // 日期格式
    std::string tag_separator = "_";         // 标签分隔符
};

/**
 * @brief 日志配置结构
 */
struct LogConfig {
    utilities::Logger::Level level = utilities::Logger::Level::INFO;
    std::string log_path = "";
    size_t max_file_size = 1024 * 1024 * 10; // 10MB
    size_t max_files = 3;
};

/**
 * @brief 主配置结构
 */
struct Config {
    // 认证配置
    std::string pixiv_cookie;
    AuthConfig auth;

    // 下载配置
    DownloadMethod download_method = DownloadMethod::DIRECT;
    std::string aria2c_path = "aria2c";
    std::string aria2c_options = "--continue=true --max-connection-per-server=4";
    int concurrency = 4; // 并发下载数
    
    // HTTP配置
    HttpConfig http;
    
    // 延迟配置
    DelayConfig delay;
    
    // 文件处理配置
    FileConflictStrategy file_conflict = FileConflictStrategy::SKIP;
    
    // 路径模板配置
    PathTemplateConfig paths;
    
    // 日志配置
    LogConfig logging;
    
    // 默认命令行参数
    std::string default_uid;
    std::vector<std::string> default_tags;
    TagFilterLogic default_tag_logic = TagFilterLogic::OR;
    std::vector<ArtworkType> default_types = {ArtworkType::ALL};
    bool default_all_works = false;
};

/**
 * @brief 配置管理器类
 * 
 * 负责加载、解析、验证和管理应用程序配置
 */
class ConfigManager {
public:
    /**
     * @brief 构造函数
     */
    ConfigManager();

    /**
     * @brief 析构函数
     */
    ~ConfigManager() = default;

    /**
     * @brief 加载配置文件
     * 
     * @param config_path 配置文件路径
     * @return true 加载成功
     * @return false 加载失败
     */
    bool LoadConfig(const std::string& config_path = "config.yaml");

    /**
     * @brief 创建默认配置文件
     * 
     * @param config_path 配置文件路径
     * @return true 创建成功
     * @return false 创建失败
     */
    bool CreateDefaultConfig(const std::string& config_path = "config.yaml");

    /**
     * @brief 验证配置
     * 
     * @return true 配置有效
     * @return false 配置无效
     */
    bool ValidateConfig();

    /**
     * @brief 获取配置
     * 
     * @return const Config& 配置引用
     */
    const Config& GetConfig() const { return config_; }

    /**
     * @brief 获取可修改的配置
     * 
     * @return Config& 配置引用
     */
    Config& GetMutableConfig() { return config_; }

    /**
     * @brief 保存配置到文件
     * 
     * @param config_path 配置文件路径
     * @return true 保存成功
     * @return false 保存失败
     */
    bool SaveConfig(const std::string& config_path = "config.yaml");

    // 枚举转换函数
    static DownloadMethod StringToDownloadMethod(const std::string& str);
    static std::string DownloadMethodToString(DownloadMethod method);
    
    static FileConflictStrategy StringToFileConflictStrategy(const std::string& str);
    static std::string FileConflictStrategyToString(FileConflictStrategy strategy);
    
    static TagFilterLogic StringToTagFilterLogic(const std::string& str);
    static std::string TagFilterLogicToString(TagFilterLogic logic);
    
    static ArtworkType StringToArtworkType(const std::string& str);
    static std::string ArtworkTypeToString(ArtworkType type);
    
    static std::vector<ArtworkType> StringToArtworkTypes(const std::string& str);
    static std::string ArtworkTypesToString(const std::vector<ArtworkType>& types);

private:
    Config config_;
    std::string config_file_path_;

    /**
     * @brief 解析YAML节点到配置结构
     * 
     * @param node YAML节点
     */
    void ParseYamlNode(const YAML::Node& node);

    /**
     * @brief 将配置结构转换为YAML节点
     * 
     * @return YAML::Node YAML节点
     */
    YAML::Node ConfigToYamlNode() const;

    /**
     * @brief 生成默认配置的YAML内容
     * 
     * @return std::string YAML内容
     */
    std::string GenerateDefaultConfigYaml() const;
};

} // namespace config
} // namespace pixiv_downloader
