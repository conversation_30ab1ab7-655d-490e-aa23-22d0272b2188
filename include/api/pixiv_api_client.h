#pragma once

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <curl/curl.h>
#include <nlohmann/json.hpp>
#include "config/config_manager.h"
#include "auth/pixiv_auth.h"

namespace pixiv_downloader {
namespace api {

/**
 * @brief HTTP响应结构
 */
struct HttpResponse {
    int status_code = 0;
    std::string body;
    std::map<std::string, std::string> headers;
    bool success = false;
    std::string error_message;
};

/**
 * @brief 作品信息结构
 */
struct ArtworkInfo {
    std::string pid;                    // 作品ID
    std::string title;                  // 标题
    std::string description;            // 描述
    std::string author_uid;             // 作者UID
    std::string author_username;        // 作者用户名
    config::ArtworkType type;           // 作品类型
    std::vector<std::string> tags;      // 标签列表
    std::string upload_date;            // 上传日期
    int page_count = 1;                 // 页数
    bool is_r18 = false;               // 是否R18
    int like_count = 0;                // 点赞数
    int bookmark_count = 0;            // 收藏数
    std::string series_title;          // 系列标题
    std::string series_id;             // 系列ID
    std::vector<std::string> image_urls; // 图片URL列表
    std::string content;               // 小说内容（仅小说类型）
    int word_count = 0;                // 字数（仅小说类型）
};

/**
 * @brief 用户信息结构
 */
struct UserInfo {
    std::string uid;                   // 用户ID
    std::string username;              // 用户名
    std::string profile_image_url;     // 头像URL
    std::string background_image_url;  // 背景图URL
    std::string comment;               // 个人简介
    bool is_followed = false;          // 是否已关注
    int following_count = 0;           // 关注数
    int follower_count = 0;            // 粉丝数
    int artwork_count = 0;             // 作品数
};

/**
 * @brief 进度回调函数类型
 * 
 * @param downloaded 已下载字节数
 * @param total 总字节数
 * @return true 继续下载
 * @return false 取消下载
 */
using ProgressCallback = std::function<bool(size_t downloaded, size_t total)>;

/**
 * @brief Pixiv API客户端类
 * 
 * 封装所有与Pixiv的HTTP/S通信，包括API调用和文件下载
 */
class PixivApiClient {
public:
    /**
     * @brief 构造函数
     * 
     * @param config 配置引用
     * @param auth 认证管理器引用
     */
    PixivApiClient(const config::Config& config, auth::PixivAuth& auth);

    /**
     * @brief 析构函数
     */
    ~PixivApiClient();

    /**
     * @brief 初始化客户端
     * 
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool Initialize();

    /**
     * @brief 清理资源
     */
    void Cleanup();

    /**
     * @brief 获取用户信息
     * 
     * @param uid 用户ID
     * @return UserInfo 用户信息，失败时返回空的UserInfo
     */
    UserInfo GetUserInfo(const std::string& uid);

    /**
     * @brief 获取用户所有作品ID列表
     * 
     * @param uid 用户ID
     * @return std::map<std::string, std::vector<std::string>> 作品ID映射（illustrations, novels）
     */
    std::map<std::string, std::vector<std::string>> GetUserArtworkIds(const std::string& uid);

    /**
     * @brief 获取作品详细信息
     * 
     * @param pid 作品ID
     * @return ArtworkInfo 作品信息，失败时返回空的ArtworkInfo
     */
    ArtworkInfo GetArtworkDetails(const std::string& pid);

    /**
     * @brief 获取多页作品的分页信息
     * 
     * @param pid 作品ID
     * @return std::vector<std::string> 图片URL列表
     */
    std::vector<std::string> GetArtworkPages(const std::string& pid);

    /**
     * @brief 获取小说详细信息
     *
     * @param pid 小说ID
     * @return ArtworkInfo 小说信息，失败时返回空的ArtworkInfo
     */
    ArtworkInfo GetNovelDetails(const std::string& pid);

    /**
     * @brief 获取用户所有作品的标签列表
     *
     * @param uid 用户ID
     * @param max_artworks 最大检查作品数量（0表示全部）
     * @return std::vector<std::string> 唯一标签列表
     */
    std::vector<std::string> GetUserTags(const std::string& uid, int max_artworks = 0);

    /**
     * @brief 下载文件
     * 
     * @param url 文件URL
     * @param output_path 输出路径
     * @param progress_callback 进度回调函数
     * @return true 下载成功
     * @return false 下载失败
     */
    bool DownloadFile(const std::string& url, const std::string& output_path, 
                     ProgressCallback progress_callback = nullptr);

    /**
     * @brief 检查URL是否可访问
     * 
     * @param url URL地址
     * @return true 可访问
     * @return false 不可访问
     */
    bool CheckUrlAccessible(const std::string& url);

    /**
     * @brief 获取文件大小（不下载文件）
     * 
     * @param url 文件URL
     * @return size_t 文件大小，失败返回0
     */
    size_t GetFileSize(const std::string& url);

    /**
     * @brief 设置请求延迟
     * 
     * @param min_delay 最小延迟（毫秒）
     * @param max_delay 最大延迟（毫秒）
     */
    void SetRequestDelay(int min_delay, int max_delay);

    /**
     * @brief 执行请求延迟
     */
    void ApplyRequestDelay();

    /**
     * @brief 获取最后一次请求的错误信息
     * 
     * @return std::string 错误信息
     */
    std::string GetLastError() const { return last_error_; }

    /**
     * @brief 设置用户代理
     * 
     * @param user_agent 用户代理字符串
     */
    void SetUserAgent(const std::string& user_agent);

    /**
     * @brief 设置自定义HTTP头部
     *
     * @param headers 头部映射
     */
    void SetCustomHeaders(const std::map<std::string, std::string>& headers);

    /**
     * @brief 获取Cookie字符串
     *
     * @return std::string Cookie字符串
     */
    std::string GetCookieString() const;

private:
    const config::Config& config_;
    auth::PixivAuth& auth_;
    void* curl_handle_;  // CURL句柄
    std::string last_error_;
    int min_delay_ms_;
    int max_delay_ms_;
    std::chrono::steady_clock::time_point last_request_time_;

    /**
     * @brief 执行HTTP GET请求
     * 
     * @param url 请求URL
     * @param headers 自定义头部
     * @return HttpResponse HTTP响应
     */
    HttpResponse PerformGetRequest(const std::string& url, 
                                  const std::map<std::string, std::string>& headers = {});

    /**
     * @brief 执行HTTP POST请求
     * 
     * @param url 请求URL
     * @param data POST数据
     * @param headers 自定义头部
     * @return HttpResponse HTTP响应
     */
    HttpResponse PerformPostRequest(const std::string& url, const std::string& data,
                                   const std::map<std::string, std::string>& headers = {});

    /**
     * @brief 设置CURL选项
     * 
     * @param headers 自定义头部
     */
    void SetupCurlOptions(const std::map<std::string, std::string>& headers = {});

    /**
     * @brief 解析JSON响应
     * 
     * @param response HTTP响应
     * @return nlohmann::json JSON对象
     */
    nlohmann::json ParseJsonResponse(const HttpResponse& response);

    /**
     * @brief 构建API URL
     * 
     * @param endpoint API端点
     * @return std::string 完整URL
     */
    std::string BuildApiUrl(const std::string& endpoint);

    /**
     * @brief 重试请求
     * 
     * @param request_func 请求函数
     * @param max_retries 最大重试次数
     * @return HttpResponse HTTP响应
     */
    HttpResponse RetryRequest(std::function<HttpResponse()> request_func, int max_retries = 3);

    /**
     * @brief 检查响应是否成功
     * 
     * @param response HTTP响应
     * @return true 成功
     * @return false 失败
     */
    bool IsResponseSuccessful(const HttpResponse& response);

    /**
     * @brief 处理API错误响应
     * 
     * @param response HTTP响应
     * @return std::string 错误信息
     */
    std::string HandleApiError(const HttpResponse& response);

    // CURL回调函数
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp);
    static size_t WriteFileCallback(void* contents, size_t size, size_t nmemb, FILE* file);
    static int ProgressCallback(void* clientp, curl_off_t dltotal, curl_off_t dlnow, 
                               curl_off_t ultotal, curl_off_t ulnow);

    // Pixiv API相关常量
    static const std::string PIXIV_BASE_URL;
    static const std::string PIXIV_API_BASE_URL;
    static const std::string USER_AGENT_DEFAULT;
    static const std::string REFERER_DEFAULT;
};

} // namespace api
} // namespace pixiv_downloader
