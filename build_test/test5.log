命令行参数解析错误: The following arguments were not expected: illust --artwork-types
PixivTagDownloader - Pixiv作品下载器 


./PixivTagDownloader [OPTIONS]


OPTIONS:
  -h,     --help              Print this help message and exit 
  -u,     --uid TEXT          目标Pixiv用户ID（必需） 
  -t,     --tags TEXT ...     标签列表（逗号分隔） 
  -l,     --logic TEXT [or]   标签过滤逻辑 (and|or|not) 
          --all-works         下载所有作品 
  -T,     --type TEXT [all]   作品类型 (illust|manga|novel|all) 
  -c,     --config TEXT [config.yaml]  
                              配置文件路径 
          --output-dir TEXT   输出根目录 
          --download-method TEXT 
                              下载方式 (direct|aria2c) 
          --threads, --concurrency INT 
                              并发下载数 
          --delay TEXT        随机延迟范围 (如: 1-3) 
          --file-conflict TEXT 
                              文件冲突策略 (skip|overwrite|rename) 
          --log-level TEXT    日志级别 (trace|debug|info|warn|error) 
          --log-path TEXT     日志文件路径 
  -i,     --interactive       强制交互模式 
          --version           显示版本信息 


使用示例:
  # 交互模式
  PixivTagDownloader
  
  # 下载指定用户的所有作品
  PixivTagDownloader -u 12345678 --all-works
  
  # 下载指定用户的特定标签作品
  PixivTagDownloader -u 12345678 -t "原创,插画" -l or
  
  # 仅下载插画类型
  PixivTagDownloader -u 12345678 -T illust --all-works
