[2025-06-15 21:02:24.633] [PixivTagDownloader] [info] 配置文件加载成功: config.yaml
[2025-06-15 21:02:24.635] [info] [:] 日志系统初始化成功
[2025-06-15 21:02:24.635] [info] [:] PixivTagDownloader 启动中...
[2025-06-15 21:02:24.635] [info] [:] Cookie设置成功，包含 3 个键值对
[2025-06-15 21:02:24.635] [warning] [:] 跳过在线Cookie验证（配置设置）
[2025-06-15 21:02:24.635] [info] [:] Pixiv 认证成功
[2025-06-15 21:02:24.636] [info] [:] API客户端初始化成功

 ____  _       _       _____             ____                      _                 _           
|  _ \(_)_  __(_)_   _|_   _|_ _  __ _   |  _ \  _____      ___ __ | | ___   __ _  __| | ___ _ __ 
| |_) | \ \/ /| \ \ / / | |/ _` |/ _` |  | | | |/ _ \ \ /\ / / '_ \| |/ _ \ / _` |/ _` |/ _ \ '__|
|  __/| |>  < | |\ V /  | | (_| | (_| |  | |_| | (_) \ V  V /| | | | | (_) | (_| | (_| |  __/ |   
|_|   |_/_/\_\|_| \_/   |_|\__,_|\__, |  |____/ \___/ \_/\_/ |_| |_|_|\___/ \__,_|\__,_|\___|_|   
                                 |___/                                                            

欢迎使用 PixivTagDownloader!
这是一个用于从Pixiv下载作品的工具。


=== PixivTagDownloader 交互模式 ===
💡 提示：在任何时候按 Ctrl+C 可以退出程序

📚 选择要下载的作品类型：
1. 插画 (Illust) - 单张图片作品
2. 漫画 (Manga) - 多页漫画作品
3. 小说 (Novel) - 文字小说作品
4. 全部 (All) - 包含所有类型

💡 提示：可多选，用逗号分隔，如：1,2
请输入选择 [4]: ✅ 已选择：插画

💡 用户ID格式说明：
   - 用户ID是纯数字，通常为8-9位
   - 示例：28925283
   - 可以从用户主页URL中获取：https://www.pixiv.net/users/28925283

请输入画师的Pixiv用户ID: ✅ 用户ID格式正确: 28925283

🎯 选择下载方式：
1. 下载所有作品 - 下载该用户的全部公开作品
2. 按手动输入的标签筛选 - 您手动指定要筛选的标签
3. 从用户现有作品的标签列表中选择 - 从该用户作品中提取标签供您选择

💡 提示：
   - 方式1适合下载喜欢画师的全部作品
   - 方式2适合您已知想要的特定标签
   - 方式3适合探索该画师的作品标签
请选择 [1]: ✅ 已选择：手动输入标签

🏷️  手动输入标签：
💡 标签输入说明：
   - 多个标签用逗号分隔，如：原创,插画,风景
   - 支持中文和英文标签
   - 标签名称区分大小写
   - 常见标签示例：原创、插画、漫画、R-18、百合、东方Project

请输入要筛选的标签: ✅ 已添加标签：原创
✅ 已添加标签：插画
📋 共添加 2 个标签

🔍 选择标签筛选逻辑：
1. AND (交集) - 作品必须包含所有选定标签
2. OR (并集) - 作品只需包含任意一个选定标签
3. NOT (排除) - 排除包含指定标签的作品

💡 选择建议：
   - AND：适合精确筛选，如同时要求'原创'和'插画'
   - OR：适合广泛收集，如包含'风景'或'人物'的作品
   - NOT：适合排除不想要的内容，如排除'R-18'标签
请选择 [2]: ✅ 已选择筛选逻辑：OR (并集)

=== 确认您的选择 ===
用户ID: 28925283
作品类型: illust
下载方式: 按标签筛选
标签: 原创, 插画
过滤逻辑: or
是否继续下载? (Y/n): [2025-06-15 21:02:24.636] [info] [:] 开始执行下载任务，用户ID: 28925283
[2025-06-15 21:02:25.662] [info] [:] 成功获取用户信息: クロソト (28925283)
[2025-06-15 21:02:26.212] [info] [:] 获取到用户作品ID: 插画/漫画 50, 小说 2
📥 正在获取作品详细信息，这可能需要一些时间...
[2025-06-15 21:02:27.112] [info] [:] 开始获取 52 个作品的详细信息...
