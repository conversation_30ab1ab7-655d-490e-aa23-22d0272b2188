### Pixiv API 接口文档

---

#### **1. 获取用户信息**
**Endpoint**: `/ajax/user/{uid}`
**方法**: `get_user_info(uid: str) -> Dict`
**描述**: 获取指定用户的基本信息
**参数**:
- `uid`: 用户ID (字符串)

**返回**:
```python
{
  "name": "用户名",
  "userId": "用户ID",
  ... # 其他用户信息字段
}
```

---

#### **2. 获取用户作品ID列表**
**Endpoint**: `/ajax/user/{uid}/profile/all`
**方法**: `get_user_artworks_ids(uid: str) -> Dict[str, List[str]]`
**描述**: 获取用户所有作品的ID（分插画/漫画和小说两类）
**参数**:
- `uid`: 用户ID

**返回**:
```python
{
  "illustrations": ["作品ID1", "作品ID2", ...],  # 插画/漫画类
  "novels": ["小说ID1", "小说ID2", ...]         # 小说类
}
```

---

#### **3. 获取作品详情**
**Endpoint**: `/ajax/illust/{pid}`
**方法**: `get_artwork_details(pid: str) -> Dict`
**描述**: 获取单幅作品（插画/漫画）的详细信息
**参数**:
- `pid`: 作品ID

**返回**:
```python
{
  "title": "作品标题",
  "pageCount": 页数,
  "tags": {"tags": [{"tag": "标签1"}, ...]},
  ... # 其他作品元数据
}
```
> 注：多页作品会自动补充 `pages` 字段（通过 `/ajax/illust/{pid}/pages`）

---

#### **4. 获取多页作品分页信息**
**Endpoint**: `/ajax/illust/{pid}/pages`
**方法**: `get_artwork_pages(pid: str) -> List[Dict]`
**描述**: 获取多页作品的所有分页数据
**参数**:
- `pid`: 作品ID

**返回**:
```python
[
  {"urls": {"original": "图片URL1"}, ...},
  {"urls": {"original": "图片URL2"}, ...}
]
```

---

#### **5. 获取小说详情**
**Endpoint**: 
- `/ajax/novel/{pid}` (元数据)
- `/ajax/novel/{pid}/content` (内容)
**方法**: `get_novel_details(pid: str) -> Dict`
**描述**: 获取小说元数据和正文内容
**参数**:
- `pid`: 小说ID

**返回**:
```python
{
  "title": "小说标题",
  "content": "小说正文",
  "tags": {"tags": [{"tag": "标签1"}, ...]},
  ... # 其他小说元数据
}
```
