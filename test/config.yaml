# PixivTagDownloader 配置文件示例
# 请根据需要修改以下配置项

# Pixiv认证Cookie（必需）
# 从浏览器中复制完整的Cookie字符串
# 获取方法：
# 1. 在浏览器中登录Pixiv
# 2. 打开开发者工具（F12）
# 3. 转到Network标签页
# 4. 刷新页面
# 5. 找到任意请求，复制Cookie头部的值
pixiv_cookie: "PHPSESSID=18104148_neDOEwTvIXZWnjvE0MIy2jc4jSRp4IC5; privacy_policy_agreement=1"

# 认证配置
auth:
  skip_online_validation: false    # 跳过在线Cookie验证（用于离线测试）
  allow_offline_mode: false         # 允许离线模式
  validation_timeout: 30           # 验证超时时间（秒）
  require_r18_permission: false    # 是否要求R18权限
  strict_cookie_validation: false  # 严格Cookie验证（设为false以兼容不完整Cookie）

# 下载配置
download_method: "direct"  # 下载方式: direct（直接下载）, aria2c（使用aria2c）
aria2c_path: "aria2c"      # aria2c可执行文件路径
aria2c_options: "--continue=true --max-connection-per-server=4 --split=4"
concurrency: 4             # 并发下载数（建议根据网络情况调整）

# HTTP配置
http:
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  referer: "https://www.pixiv.net/"
  connection_timeout: 30   # 连接超时（秒）
  read_timeout: 60         # 读取超时（秒）
  max_retries: 3           # 最大重试次数
  retry_delay: 2           # 重试延迟（秒）
  custom_headers: {}       # 自定义HTTP头部

# 请求延迟配置（避免触发反爬虫机制）
delay:
  min_delay: 1             # 最小延迟（秒）
  max_delay: 3             # 最大延迟（秒）

# 文件冲突处理策略
file_conflict: "skip"      # skip（跳过）, overwrite（覆盖）, rename（重命名）

# 路径和文件命名模板
# 可用变量：
# {uid} - 用户ID
# {username} - 用户名
# {pid} - 作品ID
# {title} - 作品标题
# {type} - 作品类型（Illust/Manga/Novel）
# {page_index} - 页面索引
# {page_count} - 总页数
# {series_title} - 系列标题
# {series_id} - 系列ID
# {upload_date} - 上传日期
# {tags} - 标签（用指定分隔符连接）
# {r18} - R18标记
# {like_count} - 点赞数
# {bookmark_count} - 收藏数
# {ext} - 文件扩展名
#
# Metadata文件命名规则：
# - 单图：与图片文件名相同，但扩展名为_info.txt
# - 多图：与目录名相同，但扩展名为_info.txt，放在子目录内
# - 小说：与小说文件名相同，但扩展名为_info.txt
paths:
  output_root_dir: "./downloads"
  artwork_path_template: "{output_root_dir}/{uid}_{username}/{type}/{series_title|No_Series}/"
  single_image_naming_template: "{upload_date}_{pid}_{title}{ext}"
  multi_image_subfolder_naming_template: "{pid}_{title}/"
  multi_image_file_naming_template: "{page_index}_{title}{ext}"
  novel_path_template: "{output_root_dir}/{uid}_{username}/Novel/{series_title|No_Series}/"
  novel_naming_template: "{upload_date}_{pid}_{title}.txt"
  metadata_filename_template: "{upload_date}_{pid}_{title}_info.txt"  # 注意：实际使用时会根据作品类型自动调整文件名
  page_index_format: "p%02d"  # 页码格式（p00, p01, p02...）
  date_format: "%Y%m%d"       # 日期格式（20231027）
  tag_separator: "_"          # 标签分隔符

# 日志配置
logging:
  level: "info"            # 日志级别: trace, debug, info, warn, error
  log_path: "./logs/"             # 日志文件路径（空表示仅控制台输出）
  max_file_size: 10485760  # 最大文件大小（字节，10MB）
  max_files: 3             # 最大文件数量

# 默认命令行参数（可被命令行参数覆盖）
defaults:
  uid: ""                  # 默认用户ID
  tags: []                 # 默认标签列表
  tag_logic: "or"          # 默认标签过滤逻辑: and, or, not
  types: "all"             # 默认作品类型: illust, manga, novel, all
  all_works: false         # 默认是否下载所有作品
