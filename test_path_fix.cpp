#include <iostream>
#include <string>
#include <map>
#include <regex>

// 简化的模板处理函数
std::string ProcessTemplate(const std::string& template_str,
                           const std::map<std::string, std::string>& variables) {
    std::string result = template_str;

    // 首先处理条件模板（如 {series_title|No_Series}）
    std::regex conditional_pattern(R"(\{([^|}]+)\|([^}]+)\})");
    std::smatch match;

    while (std::regex_search(result, match, conditional_pattern)) {
        std::string var_name = match[1].str();
        std::string default_value = match[2].str();

        auto it = variables.find(var_name);
        std::string replacement = (it != variables.end() && !it->second.empty())
                                 ? it->second : default_value;

        // 替换整个条件模板
        std::string full_match = match[0].str();
        size_t pos = result.find(full_match);
        if (pos != std::string::npos) {
            result.replace(pos, full_match.length(), replacement);
        }
    }

    // 然后替换普通模板变量
    for (const auto& [key, value] : variables) {
        std::string placeholder = "{" + key + "}";
        size_t pos = 0;
        while ((pos = result.find(placeholder, pos)) != std::string::npos) {
            result.replace(pos, placeholder.length(), value);
            pos += value.length();
        }
    }

    return result;
}

int main() {
    // 测试路径模板处理
    std::string template_str = "{output_root_dir}/{uid}_{username}/{type}/";

    std::map<std::string, std::string> variables;
    variables["output_root_dir"] = "./downloads";
    variables["uid"] = "28925283";
    variables["username"] = "クロソト";
    variables["type"] = "illust";

    std::string result = ProcessTemplate(template_str, variables);

    std::cout << "=== 路径模板处理测试 ===" << std::endl;
    std::cout << "模板: " << template_str << std::endl;
    std::cout << "结果: " << result << std::endl;

    // 测试条件模板
    std::string template_with_condition = "{output_root_dir}/{uid}_{username}/{type}/{series_title|No_Series}/";

    // 测试1：有系列标题
    variables["series_title"] = "测试系列";
    std::string result1 = ProcessTemplate(template_with_condition, variables);

    std::cout << "\n=== 条件模板测试（有系列标题） ===" << std::endl;
    std::cout << "模板: " << template_with_condition << std::endl;
    std::cout << "结果: " << result1 << std::endl;

    // 测试2：无系列标题
    variables["series_title"] = "";
    std::string result2 = ProcessTemplate(template_with_condition, variables);

    std::cout << "\n=== 条件模板测试（无系列标题） ===" << std::endl;
    std::cout << "模板: " << template_with_condition << std::endl;
    std::cout << "结果: " << result2 << std::endl;

    // 测试用户名为空的情况
    variables["username"] = "";
    std::string result3 = ProcessTemplate(template_str, variables);

    std::cout << "\n=== 空用户名测试 ===" << std::endl;
    std::cout << "模板: " << template_str << std::endl;
    std::cout << "结果: " << result3 << std::endl;

    return 0;
}
