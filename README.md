# PixivTagDownloader

一个高效的C++应用程序，用于根据用户指定的标准从Pixiv下载作品（插画、漫画、小说）。

## 主要功能

- **多种作品类型支持**：插画、漫画、小说
- **灵活的过滤选项**：按标签、作品类型、用户筛选
- **并发下载**：支持多线程并发下载，提高效率
- **多种下载方式**：内置HTTP客户端或外部aria2c
- **智能文件组织**：可配置的目录结构和文件命名模板
- **完整的元数据**：自动生成包含作品信息的元数据文件
- **强大的错误处理**：网络重试、文件冲突处理
- **用户友好界面**：支持交互式和命令行两种模式
- **详细日志记录**：可配置的多级别日志系统

## 系统要求

- **操作系统**：Windows (x86_64), Linux (x86_64, ARM64), macOS (x86_64, ARM64)
- **编译器**：支持C++20的编译器（GCC 10+, Clang 12+, MSVC 2019+）
- **依赖库**：
  - libcurl
  - nlohmann/json
  - yaml-cpp
  - spdlog
  - CLI11

## 快速开始

### 1. 编译安装

#### ArchLinux

```bash
# 安装依赖
sudo pacman -S cmake gcc curl nlohmann-json yaml-cpp spdlog cli11

# 克隆并编译
git clone <repository-url>
cd PixivTagDownloader-CPP
mkdir build && cd build
cmake ..
make -j$(nproc)
```

#### Ubuntu/Debian

```bash
# 安装依赖
sudo apt update
sudo apt install cmake g++ libcurl4-openssl-dev nlohmann-json3-dev libyaml-cpp-dev libspdlog-dev

# 克隆并编译
git clone <repository-url>
cd PixivTagDownloader-CPP
mkdir build && cd build
cmake ..
make -j$(nproc)
```

### 2. 配置

首次运行时，程序会自动创建默认配置文件 `config.yaml`：

```bash
./PixivTagDownloader
```

编辑 `config.yaml` 文件，至少需要配置：

```yaml
# 必需：从浏览器复制Pixiv的Cookie
pixiv_cookie: "your_pixiv_cookie_here"

# 可选：调整其他设置
output_root_dir: "./downloads"
concurrency: 4
```

### 3. 获取Pixiv Cookie

1. 在浏览器中登录Pixiv
2. 打开开发者工具（F12）
3. 转到Network标签页
4. 刷新页面
5. 找到任意请求，复制Cookie头部的完整值

### 4. 使用方法

#### 交互式模式

```bash
./PixivTagDownloader
```

程序会引导您完成以下步骤：
1. 选择作品类型（插画/漫画/小说）
2. 输入目标用户ID
3. 选择下载方式（全部作品/按标签筛选）
4. 确认并开始下载

#### 命令行模式

```bash
# 下载指定用户的所有作品
./PixivTagDownloader -u 12345678 --all-works

# 下载指定用户的特定标签作品
./PixivTagDownloader -u 12345678 -t "原创,插画" -l or

# 仅下载插画类型
./PixivTagDownloader -u 12345678 -T illust --all-works

# 使用自定义配置文件
./PixivTagDownloader -c custom_config.yaml -u 12345678 --all-works
```

## 命令行参数

| 参数 | 描述 | 示例 |
|------|------|------|
| `-u, --uid` | 目标用户ID（必需） | `-u 12345678` |
| `-t, --tags` | 标签列表（逗号分隔） | `-t "原创,插画,风景"` |
| `-l, --logic` | 标签过滤逻辑 | `-l and` |
| `--all-works` | 下载所有作品 | `--all-works` |
| `-T, --type` | 作品类型 | `-T illust,manga` |
| `-c, --config` | 配置文件路径 | `-c config.yaml` |
| `--output-dir` | 输出目录 | `--output-dir ./downloads` |
| `--download-method` | 下载方式 | `--download-method aria2c` |
| `--threads` | 并发线程数 | `--threads 8` |
| `--delay` | 请求延迟范围 | `--delay 1-3` |
| `--file-conflict` | 文件冲突策略 | `--file-conflict rename` |
| `--log-level` | 日志级别 | `--log-level debug` |

## 配置文件详解

配置文件使用YAML格式，支持以下主要配置项：

### 认证配置
```yaml
pixiv_cookie: "your_cookie_string"
```

### 下载配置
```yaml
download_method: "direct"  # direct 或 aria2c
concurrency: 4             # 并发下载数
```

### 路径模板
```yaml
paths:
  output_root_dir: "./downloads"
  artwork_path_template: "{output_root_dir}/{uid}_{username}/{type}/"
  single_image_naming_template: "{upload_date}_{pid}_p0_{title}{ext}"
```

### 可用模板变量

- `{uid}` - 用户ID
- `{username}` - 用户名
- `{pid}` - 作品ID
- `{title}` - 作品标题
- `{type}` - 作品类型
- `{page_index}` - 页面索引
- `{upload_date}` - 上传日期
- `{tags}` - 标签列表
- `{r18}` - R18标记
- `{ext}` - 文件扩展名

## 文件组织

下载的文件会按照配置的模板组织：

```
downloads/
├── 12345678_艺术家名/
│   ├── Illust/
│   │   ├── 20231027_87654321_p0_作品标题.jpg
│   │   ├── 87654321_info.txt
│   │   └── 多页作品/
│   │       ├── p00_作品标题.jpg
│   │       ├── p01_作品标题.jpg
│   │       └── 87654322_info.txt
│   ├── Manga/
│   └── Novel/
│       └── 系列名/
│           ├── 20231027_87654323_小说标题.txt
│           └── 87654323_info.txt
```

## 故障排除

### 常见问题

1. **Cookie无效错误**
   - 确保Cookie是最新的且包含完整信息
   - 检查Pixiv账户是否有查看R18内容的权限

2. **网络连接问题**
   - 检查网络连接
   - 调整重试次数和延迟设置
   - 考虑使用代理

3. **文件权限错误**
   - 确保输出目录有写入权限
   - 检查磁盘空间是否充足

4. **编译错误**
   - 确保编译器支持C++20
   - 检查所有依赖库是否正确安装

### 日志调试

启用详细日志以获取更多信息：

```bash
./PixivTagDownloader --log-level debug
```

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 免责声明

本工具仅供学习和个人使用。请遵守Pixiv的服务条款和相关法律法规。用户需对使用本工具的行为承担全部责任。

## 贡献

欢迎提交Issue和Pull Request来改进本项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持插画、漫画、小说下载
- 实现并发下载和生产者-消费者模型
- 提供交互式和命令行两种模式
- 完整的配置管理和错误处理
